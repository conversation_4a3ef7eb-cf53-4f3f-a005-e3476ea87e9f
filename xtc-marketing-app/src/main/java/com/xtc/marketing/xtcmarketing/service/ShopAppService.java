package com.xtc.marketing.xtcmarketing.service;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.xtcmarketing.dto.ShopDTO;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagSaveCmd;
import com.xtc.marketing.xtcmarketing.dto.query.ShopPageQry;

/**
 * 门店应用服务接口
 */
public interface ShopAppService {

    /**
     * 门店分页列表
     *
     * @param qry 参数
     * @return 门店分页列表
     */
    PageResponse<ShopDTO> pageShops(ShopPageQry qry);

    /**
     * 门店详情
     *
     * @param shopId 门店id
     * @return 门店详情
     */
    ShopDTO getShopDetail(String shopId);

    /**
     * 保存门店标签
     *
     * @param cmd 参数
     */
    void saveShopTag(ShopTagSaveCmd cmd);

}
