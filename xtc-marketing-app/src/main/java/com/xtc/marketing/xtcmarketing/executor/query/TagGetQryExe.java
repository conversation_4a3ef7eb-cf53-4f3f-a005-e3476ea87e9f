package com.xtc.marketing.xtcmarketing.executor.query;

import com.xtc.marketing.xtcmarketing.dao.TagDao;
import com.xtc.marketing.xtcmarketing.dataobject.TagDO;
import com.xtc.marketing.xtcmarketing.exception.BizErrorCode;
import com.xtc.marketing.xtcmarketing.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 标签查询执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TagGetQryExe {

    private final TagDao tagDao;

    /**
     * 查询标签
     *
     * @param tagCode 标签代码
     * @return 标签
     */
    public TagDO byTagCode(String tagCode) {
        return tagDao.getByTagCode(tagCode)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_TAG_TagNotExists));
    }

}
