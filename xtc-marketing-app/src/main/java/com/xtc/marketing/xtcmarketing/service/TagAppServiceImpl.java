package com.xtc.marketing.xtcmarketing.service;

import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.xtcmarketing.converter.TagConverter;
import com.xtc.marketing.xtcmarketing.dao.TagDao;
import com.xtc.marketing.xtcmarketing.dataobject.TagDO;
import com.xtc.marketing.xtcmarketing.dto.TagDTO;
import com.xtc.marketing.xtcmarketing.dto.command.TagCreateCmd;
import com.xtc.marketing.xtcmarketing.dto.command.TagEditCmd;
import com.xtc.marketing.xtcmarketing.dto.query.TagPageQry;
import com.xtc.marketing.xtcmarketing.exception.BizErrorCode;
import com.xtc.marketing.xtcmarketing.exception.BizException;
import com.xtc.marketing.xtcmarketing.util.BeanCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 标签应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TagAppServiceImpl implements TagAppService {

    // infra
    private final TagDao tagDao;
    private final TagConverter tagConverter;

    @Override
    public PageResponse<TagDTO> pageTags(TagPageQry qry) {
        Page<TagDO> page = tagDao.pageBy(qry);
        List<TagDTO> records = tagConverter.doToDto(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public TagDTO tagDetail(long id) {
        TagDO tagDO = tagDao.getByIdOpt(id)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_TAG_TagNotExists));
        return tagConverter.doToDto(tagDO);
    }

    @Override
    public Long createTag(TagCreateCmd cmd) {
        // 校验标签序号不能重复
        boolean existsByTagNo = tagDao.existsByTagNo(cmd.getTagNo());
        if (existsByTagNo) {
            throw BizException.of(BizErrorCode.B_TAG_TagNoAlreadyExists);
        }
        // 校验标签代码不能重复
        boolean existsByTagCode = tagDao.existsByTagCode(cmd.getTagCode());
        if (existsByTagCode) {
            throw BizException.of(BizErrorCode.B_TAG_TagCodeAlreadyExists);
        }
        // 新增标签
        TagDO tagDO = BeanCopier.copy(cmd, TagDO::new);
        tagDao.save(tagDO);
        return tagDO.getId();
    }

    @Override
    public void editTag(long id, TagEditCmd cmd) {
        // 校验标签是否存在
        boolean notExists = tagDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_TAG_TagNotExists);
        }
        // 校验标签序号不能重复
        boolean existsByTagNo = tagDao.existsByTagNoExcludeId(cmd.getTagNo(), id);
        if (existsByTagNo) {
            throw BizException.of(BizErrorCode.B_TAG_TagNoAlreadyExists);
        }
        // 校验标签代码不能重复
        boolean existsByTagCode = tagDao.existsByTagCodeExcludeId(cmd.getTagCode(), id);
        if (existsByTagCode) {
            throw BizException.of(BizErrorCode.B_TAG_TagCodeAlreadyExists);
        }
        // 编辑标签
        TagDO tagDO = BeanCopier.copy(cmd, TagDO::new);
        tagDao.updateById(id, tagDO);
    }

    @Override
    public void removeTag(long id) {
        // 参数校验
        boolean notExists = tagDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_TAG_TagNotExists);
        }
        // 删除标签
        tagDao.removeById(id);
    }

    @Override
    public List<TagDTO> listEnabledTags() {
        List<TagDO> tags = tagDao.listEnabled();
        return tagConverter.doToDto(tags);
    }

}
