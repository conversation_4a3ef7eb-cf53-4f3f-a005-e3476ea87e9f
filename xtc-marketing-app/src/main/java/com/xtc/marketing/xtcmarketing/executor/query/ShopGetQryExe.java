package com.xtc.marketing.xtcmarketing.executor.query;

import com.xtc.marketing.xtcmarketing.dao.ShopDao;
import com.xtc.marketing.xtcmarketing.dataobject.ShopDO;
import com.xtc.marketing.xtcmarketing.exception.BizErrorCode;
import com.xtc.marketing.xtcmarketing.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 门店查询执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ShopGetQryExe {

    private final ShopDao shopDao;

    /**
     * 查询门店
     *
     * @param shopId 门店id
     * @return 门店
     */
    public ShopDO byShopId(String shopId) {
        return shopDao.getByShopId(shopId)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_SHOP_ShopNotExists));
    }

}
