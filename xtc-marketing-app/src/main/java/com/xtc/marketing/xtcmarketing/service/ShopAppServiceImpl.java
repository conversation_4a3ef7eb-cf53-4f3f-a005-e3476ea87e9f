package com.xtc.marketing.xtcmarketing.service;

import com.google.common.collect.Lists;
import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.xtcmarketing.converter.ShopConverter;
import com.xtc.marketing.xtcmarketing.dao.ShopDao;
import com.xtc.marketing.xtcmarketing.dao.ShopTagDao;
import com.xtc.marketing.xtcmarketing.dataobject.ShopDO;
import com.xtc.marketing.xtcmarketing.dataobject.ShopTagDO;
import com.xtc.marketing.xtcmarketing.dataobject.TagDO;
import com.xtc.marketing.xtcmarketing.dto.ShopDTO;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagSaveCmd;
import com.xtc.marketing.xtcmarketing.dto.query.ShopPageQry;
import com.xtc.marketing.xtcmarketing.executor.query.ShopGetQryExe;
import com.xtc.marketing.xtcmarketing.executor.query.TagGetQryExe;
import com.xtc.marketing.xtcmarketing.validator.TagValueValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 门店应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopAppServiceImpl implements ShopAppService {

    // executor
    private final ShopGetQryExe shopGetQryExe;
    private final TagGetQryExe tagGetQryExe;
    // infra
    private final ShopDao shopDao;
    private final ShopTagDao shopTagDao;
    private final ShopConverter shopConverter;

    @Override
    public PageResponse<ShopDTO> pageShops(ShopPageQry qry) {
        Page<ShopDO> page = shopDao.pageBy(qry);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }
        // 汇总门店id集合
        Set<String> shopIds = page.getRecords().stream().map(ShopDO::getShopId).collect(Collectors.toUnmodifiableSet());
        Map<String, ShopTagDO> shopTagMap = shopTagDao.mapByShopIds(shopIds);
        // 将门店数据转换为 DTO，并关联门店标签
        List<ShopDTO> records = page.getRecords().stream()
                .map(shop -> shopConverter.doToDto(shop, shopTagMap.get(shop.getShopId())))
                .toList();
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public ShopDTO getShopDetail(String shopId) {
        ShopDO shopDO = shopGetQryExe.byShopId(shopId);
        ShopTagDO shopTagDO = shopTagDao.getByShopId(shopId).orElse(null);
        return shopConverter.doToDto(shopDO, shopTagDO);
    }

    @Override
    public void saveShopTag(ShopTagSaveCmd cmd) {
        ShopDO shopDO = shopGetQryExe.byShopId(cmd.getShopId());
        // 查询标签，并验证标签值在允许的范围内
        TagDO tagDO = tagGetQryExe.byTagCode(cmd.getTagCode());
        TagValueValidator.validateValueInRange(tagDO, cmd.getTagValue());
        // 标签已存在则修改，不存在则将新标签数据添加到集合开头
        Optional<ShopTagDO> shopTagOpt = shopTagDao.getByShopId(shopDO.getShopId());
        List<ShopTagDO.Tag> tags = shopTagOpt.map(ShopTagDO::getTags).map(Lists::newArrayList)
                .orElse(Lists.newArrayListWithCapacity(1));
        tags.stream()
                .filter(tag -> tagDO.getTagCode().equals(tag.getTagCode()))
                .findAny()
                .ifPresentOrElse(
                        tag -> {
                            // 如果已存在相同标签代码，则修改标签值和tagTime
                            tag.setTagValue(cmd.getTagValue());
                            tag.setTagTime(LocalDateTime.now());
                        },
                        () -> {
                            // 如果不存在，则创建新标签信息
                            ShopTagDO.Tag newTag = ShopTagDO.Tag.builder()
                                    .tagValue(cmd.getTagValue())
                                    .tagTime(LocalDateTime.now())
                                    .tagType(tagDO.getTagType())
                                    .tagCode(tagDO.getTagCode())
                                    .tagName(tagDO.getTagName())
                                    .tagNo(tagDO.getTagNo())
                                    .build();
                            tags.addFirst(newTag);
                        }
                );
        // 保存门店标签数据
        shopTagOpt.ifPresentOrElse(
                shopTagDO -> shopTagDao.updateTagsByShopId(shopDO.getShopId(), tags),
                () -> shopTagDao.save(ShopTagDO.builder().shopId(shopDO.getShopId()).tags(tags).build())
        );
    }

}
