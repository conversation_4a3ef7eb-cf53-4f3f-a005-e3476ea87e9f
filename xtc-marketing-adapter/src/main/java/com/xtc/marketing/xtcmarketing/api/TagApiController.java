package com.xtc.marketing.xtcmarketing.api;

import com.xtc.marketing.dto.MultiResponse;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.xtcmarketing.dto.TagDTO;
import com.xtc.marketing.xtcmarketing.dto.command.TagCreateCmd;
import com.xtc.marketing.xtcmarketing.dto.command.TagEditCmd;
import com.xtc.marketing.xtcmarketing.dto.query.TagPageQry;
import com.xtc.marketing.xtcmarketing.service.TagAppService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class TagApiController {

    private final TagAppService tagAppService;

    /**
     * 标签分页列表
     *
     * @param qry 参数
     * @return 标签分页列表
     */
    @GetMapping("/tags")
    public PageResponse<TagDTO> pageTags(@Valid TagPageQry qry) {
        return tagAppService.pageTags(qry);
    }

    /**
     * 标签详情
     *
     * @param id 标签id
     * @return 标签详情
     */
    @GetMapping("/tags/{id}")
    public SingleResponse<TagDTO> tagDetail(@NotNull @Positive @PathVariable("id") Long id) {
        TagDTO detail = tagAppService.tagDetail(id);
        return SingleResponse.of(detail);
    }

    /**
     * 新增标签
     *
     * @param cmd 参数
     * @return 标签id
     */
    @PostMapping("/tags")
    public SingleResponse<Long> createTag(@Valid @RequestBody TagCreateCmd cmd) {
        Long id = tagAppService.createTag(cmd);
        return SingleResponse.of(id);
    }

    /**
     * 修改标签
     *
     * @param id  标签id
     * @param cmd 参数
     * @return 执行结果
     */
    @PutMapping("/tags/{id}")
    public Response editTag(@NotNull @Positive @PathVariable("id") Long id,
                            @Valid @RequestBody TagEditCmd cmd) {
        tagAppService.editTag(id, cmd);
        return Response.buildSuccess();
    }

    /**
     * 删除标签
     *
     * @param id 标签id
     * @return 执行结果
     */
    @DeleteMapping("/tags/{id}")
    public Response removeTag(@NotNull @Positive @PathVariable("id") Long id) {
        tagAppService.removeTag(id);
        return Response.buildSuccess();
    }

    /**
     * 启用标签列表
     *
     * @return 标签列表
     * @apiNote 排序规则：标签类型顺序、更新时间倒序
     */
    @GetMapping("/tag/enabled")
    public MultiResponse<TagDTO> listEnabledTags() {
        List<TagDTO> dto = tagAppService.listEnabledTags();
        return MultiResponse.of(dto);
    }

}
