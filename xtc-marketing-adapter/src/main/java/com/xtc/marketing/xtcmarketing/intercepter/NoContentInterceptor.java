package com.xtc.marketing.xtcmarketing.intercepter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 无内容拦截器，遇到 OPTIONS HEAD 请求则直接返回 204 成功
 */
@Component
public class NoContentInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        // OPTIONS HEAD 请求则直接返回 204 成功
        if (HttpMethod.OPTIONS.matches(request.getMethod()) || HttpMethod.HEAD.matches(request.getMethod())) {
            response.setStatus(HttpStatus.NO_CONTENT.value());
            return false;
        }
        return true;
    }

}
