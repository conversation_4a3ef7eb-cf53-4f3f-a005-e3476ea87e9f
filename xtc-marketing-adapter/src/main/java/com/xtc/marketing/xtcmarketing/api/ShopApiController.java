package com.xtc.marketing.xtcmarketing.api;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.xtcmarketing.dto.ShopDTO;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagSaveCmd;
import com.xtc.marketing.xtcmarketing.dto.query.ShopPageQry;
import com.xtc.marketing.xtcmarketing.service.ShopAppService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 门店接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class ShopApiController {

    private final ShopAppService shopAppService;

    /**
     * 门店分页列表
     *
     * @param qry 参数
     * @return 门店分页列表
     */
    @GetMapping("/shops")
    public PageResponse<ShopDTO> pageShops(@Valid ShopPageQry qry) {
        return shopAppService.pageShops(qry);
    }

    /**
     * 门店详情
     *
     * @param shopId 门店id
     * @return 门店详情
     */
    @GetMapping("/shops/{shopId}")
    public SingleResponse<ShopDTO> getShopDetail(@NotBlank @Length(max = 50) @PathVariable("shopId") String shopId) {
        ShopDTO shopDTO = shopAppService.getShopDetail(shopId);
        return SingleResponse.of(shopDTO);
    }

    /**
     * 保存门店标签
     *
     * @param cmd 参数
     */
    @PostMapping("/shop-tags")
    public Response saveShopTag(@Valid @RequestBody ShopTagSaveCmd cmd) {
        shopAppService.saveShopTag(cmd);
        return Response.buildSuccess();
    }

}
