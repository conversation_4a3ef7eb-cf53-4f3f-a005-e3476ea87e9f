package com.xtc.marketing.xtcmarketing.config;

import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.servlet.HandlerMapping;

import java.io.*;
import java.nio.charset.Charset;
import java.util.*;

/**
 * 请求 body 读取包装类，并对请求参数进行安全过滤
 */
@Slf4j
public class RequestBodyReaderWrapper extends HttpServletRequestWrapper {

    /**
     * 不允许的关键词
     */
    private static final Set<String> NOT_ALLOWED_KEY_WORDS = HashSet.newHashSet(0);
    /**
     * 不允许的 sql 关键词
     */
    private static final String NOT_ALLOWED_SQL_KEY_WORDS = "and|exec|insert|select|delete|update|count|*|%|chr|mid|master|truncate|char|declare|or";
    /**
     * 替换字符串
     */
    private static final String REPLACED_STRING = "";

    static {
        String[] keyStr = NOT_ALLOWED_SQL_KEY_WORDS.split("\\|");
        Collections.addAll(NOT_ALLOWED_KEY_WORDS, keyStr);
    }

    /**
     * 存储 body 数据的容器
     */
    private final byte[] body;

    public RequestBodyReaderWrapper(HttpServletRequest request) {
        super(request);
        // 存储body数据
        String bodyStr = getBodyString(request);
        body = bodyStr.getBytes(Charset.defaultCharset());
    }

    @Override
    public BufferedReader getReader() {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() {
        final ByteArrayInputStream inputStream = new ByteArrayInputStream(body);
        return new ServletInputStream() {
            @Override
            public int read() {
                return inputStream.read();
            }

            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
                throw new UnsupportedOperationException();
            }
        };
    }

    /**
     * 获取请求 body 字符串
     *
     * @return body 字符串
     */
    public String getBodyString() {
        final InputStream inputStream = new ByteArrayInputStream(body);
        return inputStreamToString(inputStream);
    }

    /**
     * 获取请求 body
     *
     * @return body 字符串
     */
    public Map<String, Object> getBodyParams() {
        String bodyString = this.getBodyString();
        return GsonUtil.jsonToMap(bodyString);
    }

    /**
     * 获取 URL 请求参数
     *
     * @param excludeName 需要排除的字段名称
     * @return URL 请求参数 Map
     */
    public Map<String, Object> getUrlParams(String excludeName) {
        Enumeration<String> paramNames = this.getParameterNames();
        Map<String, Object> params = HashMap.newHashMap(10);
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            if (excludeName.equals(paramName)) {
                continue;
            }
            Object paramValue = this.getParameter(paramName);
            params.put(paramName, paramValue);
        }
        return params;
    }

    /**
     * 获取URL路径参数
     *
     * @return URL 路径参数 Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getPathParams() {
        try {
            return (Map<String, Object>) this.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
        } catch (Exception e) {
            log.warn("request getPathParams error.", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取请求的所有参数（url、path、body）
     *
     * @return 请求的所有参数（url、path、body）
     */
    public SortedMap<String, Object> getAllParams() {
        return getAllParams("");
    }

    /**
     * 获取请求的所有参数（url，body）
     *
     * @param excludeName 需要排除的字段名称
     * @return 请求的所有参数，url，body
     */
    public SortedMap<String, Object> getAllParams(String excludeName) {
        // 获取URL上的参数
        Map<String, Object> urlParams = this.getUrlParams(excludeName);
        SortedMap<String, Object> allParams = new TreeMap<>(urlParams);
        // 获取path路径参数
        Map<String, Object> pathParams = this.getPathParams();
        allParams.putAll(pathParams);
        // 获取body参数，get请求不需要拿body参数
        if (!HttpMethod.GET.name().equals(this.getMethod())) {
            Map<String, Object> bodyParams = this.getBodyParams();
            allParams.putAll(bodyParams);
        }
        return allParams;
    }

    /**
     * 获取请求 body 字符串
     *
     * @param request 请求
     * @return body 字符串
     */
    private String getBodyString(final ServletRequest request) {
        try {
            return inputStreamToString(request.getInputStream());
        } catch (IOException e) {
            log.error("获取请求 body 异常", e);
            throw SysException.of(SysErrorCode.S_UNKNOWN_ERROR, e);
        }
    }

    /**
     * 将 inputStream 里的数据读取出来并转换成字符串
     *
     * @param inputStream body 输入流
     * @return body 字符串
     */
    private String inputStreamToString(InputStream inputStream) {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, Charset.defaultCharset()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            log.error("获取请求 body 异常", e);
            throw SysException.of(SysErrorCode.S_UNKNOWN_ERROR, e);
        }
        return this.cleanXssAndSqlAttack(sb.toString());
    }

    /**
     * 清除 xss 和 sql 注入攻击字符
     *
     * @param value 参数值
     * @return 过滤后的字符串
     */
    private String cleanXssAndSqlAttack(String value) {
        String cleanValue = value;
        // 字符替换，不要加转义字符
        cleanValue = cleanValue.replace("<", "&lt;").replace(">", "&gt;");
        cleanValue = cleanValue.replace("'", "&#39;");
        cleanValue = cleanValue.replace("<script", "");
        cleanValue = cleanValue.replace("jndi:", "");
        cleanValue = cleanValue.replace("rmi:", "");
        // 正则替换
        cleanValue = cleanValue.replaceAll("eval\\((.*)\\)", "");
        cleanValue = cleanValue.replaceAll("[\"']\\s*javascript:(.*)[\"']", "\"\"");
        cleanValue = cleanSqlKeyWords(cleanValue);
        return cleanValue;
    }

    /**
     * 清除 sql 注入攻击字符
     *
     * @param value 参数值
     * @return 过滤后的字符串
     */
    private String cleanSqlKeyWords(String value) {
        String cleanValue = value;
        for (String keyword : NOT_ALLOWED_KEY_WORDS) {
            if (cleanValue.length() > keyword.length() + 4) {
                // 检查 " keyword" 情况
                String word1 = " " + keyword;
                cleanValue = cleanSqlKeyWord(value, cleanValue, word1);
                // 检查 "keyword " 情况
                String word2 = keyword + " ";
                cleanValue = cleanSqlKeyWord(value, cleanValue, word2);
                // 检查 " keyword " 情况
                String word3 = " " + keyword + " ";
                cleanValue = cleanSqlKeyWord(value, cleanValue, word3);
            }
        }
        return cleanValue;
    }

    /**
     * 清除 sql 注入攻击字符
     *
     * @param value      参数值
     * @param cleanValue 过滤后的字符串
     * @param word       关键词
     * @return 过滤后的字符串
     */
    private static String cleanSqlKeyWord(String value, String cleanValue, String word) {
        if (cleanValue.contains(word)) {
            cleanValue = StringUtils.replace(cleanValue, word, REPLACED_STRING);
            log.error("参数中包含不允许的sql关键词，已被过滤 [{}] 参数 [{}] 过滤后 [{}]", word, value, cleanValue);
        }
        return cleanValue;
    }

}
