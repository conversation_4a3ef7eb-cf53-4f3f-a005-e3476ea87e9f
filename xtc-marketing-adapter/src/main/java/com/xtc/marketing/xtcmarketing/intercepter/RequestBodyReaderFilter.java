package com.xtc.marketing.xtcmarketing.intercepter;

import com.xtc.marketing.xtcmarketing.config.RequestBodyReaderWrapper;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 读取请求体的过滤器
 */
@Order(1)
@Component
public class RequestBodyReaderFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain filterChain) throws IOException, ServletException {
        // 过滤掉文件上传请求
        if (StringUtils.isNotBlank(request.getContentType())
                && request.getContentType().contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
            filterChain.doFilter(request, response);
        } else {
            // 读取body里的参数
            ServletRequest requestBodyReaderWrapper = new RequestBodyReaderWrapper((HttpServletRequest) request);
            filterChain.doFilter(requestBodyReaderWrapper, response);
        }
    }

}
