version: v2beta1
name: xtc-marketing

# This is a list of `pipelines` that DevSpace can execute (you can define your own)
pipelines:
  # This is the pipeline for the main command: `devspace dev` (or `devspace run-pipeline dev`)
  dev:
    run: |-
      chmod 755 devspace_start.sh  # Make the script executable
      start_dev app                # Start dev mode "app" (see "dev" section)

# This is a list of `dev` containers that are based on the containers created by your deployments
dev:
  app:
    # Search for the container that runs this image
    labelSelector:
      app.kubernetes.io/name: xtc-marketing
    # Replace the container image with this dev-optimized image (allows to skip image building during development)
    devImage: hub.okii.com/base/hotswapagent/jdk21-hotswapagent-jbrsdk:1.1
    # Env can be used to add environment variables to the container
    env:
      - name: SPRING_PROFILES_ACTIVE
        value: test
      - name: HOTSWAP_AGENT
        value: -XX:+AllowEnhancedClassRedefinition -XX:HotswapAgent=fatjar --add-opens=java.base/java.net=ALL-UNNAMED
      - name: REMOTE_DEBUG
        value: -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005
      - name: ELASTIC_APM_AGENT
        value: -javaagent:elastic-apm-agent.jar
      - name: ELASTIC_APM_CAPTURE_BODY
        value: all
      - name: ELASTIC_APM_CENTRAL_CONFIG
        value: "false"
      - name: ELASTIC_APM_ENABLE_LOG_CORRELATION
        value: "true"
      - name: ELASTIC_APM_ENABLED
        value: "true"
      - name: ELASTIC_APM_SANITIZE_FIELD_NAMES
        value: password, passwd, pwd, secret, *key, *token*, *session*, *credit*, *card*, set-cookie
      - name: ELASTIC_APM_SECRET_TOKEN
        value: 6Opg3uIyNp20Rt702K01nJq3
      - name: ELASTIC_APM_SERVER_URL
        value: https://es-apm-server.okii.com
      - name: ELASTIC_APM_ENVIRONMENT
        value: xtc-marketing-devspace
      - name: ELASTIC_APM_SERVICE_NAME
        value: xtc-marketing
    # Forward the following ports to be able access your application via localhost
    ports:
      - port: 10000:8080
      - port: 20000:5005
    # Sync files between the local filesystem and the development container
    sync:
      - path: devspace.yaml
        file: true
      - path: devspace_start.sh
        file: true
      - path: "start/target/start.jar:start.jar"
        file: true
      - path: xtc-marketing-adapter/target/classes:classes/adapter
      - path: xtc-marketing-app/target/classes:classes/app
      - path: xtc-marketing-client/target/classes:classes/client
      - path: xtc-marketing-domain/target/classes:classes/domain
      - path: xtc-marketing-infrastructure/target/classes:classes/infrastructure
      - path: xtc-marketing-common/target/classes:classes/common
      - path: start/target/classes:classes/start
    # Open a terminal and use the following command to start it
    terminal:
      command: ./devspace_start.sh
    # Make the following commands from my local machine available inside the dev container
    proxyCommands:
      - command: devspace
      - command: kubectl
      - gitCredentials: true

# Use the `commands` section to define repeatable dev workflows for this project
commands:
  start:
    description: Start the application.
    command: |
      devspace enter --pod $HOSTNAME -c xtc-marketing -- bash -c '
        java $JAVA_OPTS $HOTSWAP_AGENT $ELASTIC_APM_AGENT $REMOTE_DEBUG -jar start.jar > >(tee /dev/stdout_pod) 2> >(tee /dev/stderr_pod)
      '
  logs:
    description: Print logs of the application.
    command: devspace logs -f --pod $HOSTNAME -c xtc-marketing
