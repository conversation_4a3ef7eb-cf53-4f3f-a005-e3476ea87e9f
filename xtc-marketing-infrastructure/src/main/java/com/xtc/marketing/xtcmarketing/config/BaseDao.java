package com.xtc.marketing.xtcmarketing.config;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.mybatisflex.core.util.ClassUtil;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 基础数据访问类，所有的 Dao 都继承此类，提供公共基础方法增强
 * <p>例：public class UserDao extends BaseDao<UserMapper, UserDO></p>
 *
 * @param <M> BaseMapper 对象
 * @param <T> 实体对象
 */
@Slf4j
public abstract class BaseDao<M extends BaseMapper<T>, T extends BaseDO> extends ServiceImpl<M, T> {

    /**
     * 批量更新数据量
     */
    public static final int BATCH_SIZE = 100;
    /**
     * 限制数据量：单条
     */
    public static final int LIMIT_ONE = 1;
    /**
     * 限制数据量：列表
     */
    public static final int LIMIT_LIST = 200;
    /**
     * 限制数据量：分页页数
     */
    public static final int LIMIT_PAGE_INDEX = 500;
    /**
     * 限制数据量：分页数量
     */
    public static final int LIMIT_PAGE_SIZE = 200;

    /**
     * 判断参数对象包含 WHERE 查询条件
     *
     * @param entity 参数对象
     * @return 执行结果
     */
    public boolean noCondition(Object entity) {
        return !QueryWrapper.create(entity).hasCondition();
    }

    /**
     * 查询数据 Map 列表
     *
     * @param queryColumn 查询列
     * @param ids         数据id集合
     * @param keyMapper   键映射函数
     * @return 数据 Map 列表
     */
    public Map<String, T> mapBy(QueryColumn queryColumn, Collection<String> ids, Function<T, String> keyMapper) {
        if (If.isEmpty(ids) || ids.size() > LIMIT_LIST) {
            return Map.of();
        }
        List<T> list = queryChain()
                .where(queryColumn.in(ids))
                .orderBy("id", true)
                .limit(LIMIT_LIST)
                .list();
        if (If.isEmpty(list)) {
            return Map.of();
        }
        return list.stream().collect(Collectors.toMap(keyMapper, Function.identity()));
    }

    /**
     * 更新数据
     *
     * @param id     数据id
     * @param entity 数据
     */
    public void updateById(long id, T entity) {
        if (noCondition(entity)) {
            return;
        }
        entity.setId(id);
        updateById(entity);
    }

    /**
     * 批量更新
     *
     * @param datas             数据集合
     * @param notEffective      判断单条数据无效
     * @param queryChainBuilder 条件构建
     */
    @SuppressWarnings("unchecked")
    public void updateBatchByQuery(Collection<T> datas, Predicate<T> notEffective, Function<T, QueryChain<T>> queryChainBuilder) {
        if (If.isEmpty(datas)) {
            return;
        }
        Class<BaseMapper<T>> usefulClass = (Class<BaseMapper<T>>) ClassUtil.getUsefulClass(getMapper().getClass());
        Db.executeBatch(datas, BATCH_SIZE, usefulClass, (mapper, data) -> {
            // 判断单条数据无效
            if (notEffective.test(data)) {
                log.info("数据无效跳过 {}", data);
                return;
            }
            QueryChain<T> queryChain = queryChainBuilder.apply(data).from(data.getClass());
            mapper.updateByQuery(data, queryChain);
        });
    }

    /**
     * 批量插入或者更新
     *
     * @param datas             数据集合
     * @param notEffective      判断单条数据无效
     * @param queryChainBuilder 条件构建
     */
    @SuppressWarnings("unchecked")
    public void insertOrUpdateBatchByQuery(Collection<T> datas, Predicate<T> notEffective, Function<T, QueryChain<T>> queryChainBuilder) {
        if (If.isEmpty(datas)) {
            return;
        }
        Class<BaseMapper<T>> usefulClass = (Class<BaseMapper<T>>) ClassUtil.getUsefulClass(getMapper().getClass());
        Db.executeBatch(datas, BATCH_SIZE, usefulClass, (mapper, data) -> {
            // 判断单条数据无效
            if (notEffective.test(data)) {
                log.info("数据无效跳过 {}", data);
                return;
            }
            // 判断数据存在
            QueryChain<T> queryChain = queryChainBuilder.apply(data).from(data.getClass());
            boolean exists = queryChain.exists();
            // 存在则更新，否则插入
            if (exists) {
                mapper.updateByQuery(data, queryChain);
            } else {
                mapper.insert(data);
            }
        });
    }

}
