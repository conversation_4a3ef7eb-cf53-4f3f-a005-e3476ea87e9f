package com.xtc.marketing.xtcmarketing.config;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 基础数据对象
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
public abstract class BaseDO {

    /**
     * 唯一标识，使用 雪花算法 生成ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;
    /**
     * 更新人
     */
    @Column(typeHandler = MyBatisFlexGsonTypeHandler.class)
    private Operator updateBy;
    /**
     * 创建人
     */
    @Column(typeHandler = MyBatisFlexGsonTypeHandler.class)
    private Operator createBy;
    /**
     * 删除标识
     */
    @Column(isLogicDelete = true, onInsertValue = "0")
    private Boolean deleted;
    /**
     * 更新时间
     */
    @Column(onInsertValue = "now()", onUpdateValue = "now()")
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    @Column(onInsertValue = "now()")
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Operator {

        /**
         * 操作人代码
         */
        private String code;
        /**
         * 操作人名称
         */
        private String name;

        /**
         * 创建系统操作人
         *
         * @return 系统操作人
         */
        public static Operator ofSystem() {
            return of("system", "系统");
        }

        /**
         * 创建操作人
         *
         * @param code 操作人代码
         * @param name 操作人名称
         * @return 操作人
         */
        public static Operator of(String code, String name) {
            return Operator.builder().code(code).name(name).build();
        }

    }

}
