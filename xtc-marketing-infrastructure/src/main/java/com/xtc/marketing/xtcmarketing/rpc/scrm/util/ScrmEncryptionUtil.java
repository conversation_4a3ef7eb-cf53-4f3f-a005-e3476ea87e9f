package com.xtc.marketing.xtcmarketing.rpc.scrm.util;

import com.xtc.marketing.xtcmarketing.util.DateUtil;
import com.xtc.marketing.xtcmarketing.util.Md5Encoder;

import java.util.SortedMap;
import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * SCRM加密工具类
 */
public class ScrmEncryptionUtil {

    private ScrmEncryptionUtil() {
    }

    /**
     * 生成随机字符串
     *
     * @param length 长度
     * @return 随机字符串
     */
    public static String generateNonce(int length) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid.substring(0, Math.min(length, uuid.length()));
    }

    /**
     * 生成签名
     *
     * @param accessId     访问ID
     * @param accessSecret 访问密钥
     * @param ts           时间戳
     * @param nonce        随机字符串
     * @param extraParams  额外参数
     * @return 签名
     */
    public static String generateSignature(String accessId, String accessSecret, long ts, String nonce, SortedMap<String, Object> extraParams) {
        // 编排签名参数
        SortedMap<String, Object> signatureParams = new TreeMap<>();
        signatureParams.put("accessId", accessId);
        signatureParams.put("accessSecret", accessSecret);
        signatureParams.put("ts", ts);
        signatureParams.put("nonce", nonce);
        
        // 添加额外参数
        if (extraParams != null) {
            signatureParams.putAll(extraParams);
        }
        
        String signatureStr = signatureParams.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        
        // MD5加密，大写32位
        return Md5Encoder.encode(signatureStr).toUpperCase();
    }

    /**
     * 生成请求参数字符串
     *
     * @param accessId  访问ID
     * @param ts        时间戳
     * @param nonce     随机字符串
     * @param signature 签名
     * @param extraParams 额外参数
     * @return 参数字符串
     */
    public static String generateParamString(String accessId, long ts, String nonce, String signature, SortedMap<String, Object> extraParams) {
        SortedMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("accessId", accessId);
        paramMap.put("ts", ts);
        paramMap.put("nonce", nonce);
        paramMap.put("signature", signature);
        
        // 添加额外参数
        if (extraParams != null) {
            paramMap.putAll(extraParams);
        }
        
        return paramMap.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
    }

    /**
     * 创建加密参数
     *
     * @param accessId     访问ID
     * @param accessSecret 访问密钥
     * @param extraParams  额外参数
     * @return 加密参数对象
     */
    public static EncryptionParams createEncryptionParams(String accessId, String accessSecret, SortedMap<String, Object> extraParams) {
        String nonce = generateNonce(5);
        long ts = DateUtil.nowEpochSecond();
        String signature = generateSignature(accessId, accessSecret, ts, nonce, extraParams);
        
        return EncryptionParams.builder()
                .accessId(accessId)
                .accessSecret(accessSecret)
                .ts(ts)
                .nonce(nonce)
                .signature(signature)
                .build();
    }

    /**
     * 加密参数对象
     */
    @lombok.Builder
    @lombok.Getter
    public static class EncryptionParams {
        private String accessId;
        private String accessSecret;
        private long ts;
        private String nonce;
        private String signature;
    }

}
