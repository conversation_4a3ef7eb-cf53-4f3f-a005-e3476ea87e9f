package com.xtc.marketing.xtcmarketing.rpc.scrm;

import com.xtc.marketing.xtcmarketing.constant.SystemConstant;
import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.rpc.scrm.request.BaseScrmRequest;
import com.xtc.marketing.xtcmarketing.rpc.scrm.request.GetSecondAgentRequest;
import com.xtc.marketing.xtcmarketing.rpc.scrm.response.BaseScrmResponse;
import com.xtc.marketing.xtcmarketing.rpc.scrm.response.GetSecondAgentResponse;
import com.xtc.marketing.xtcmarketing.rpc.scrm.util.ScrmEncryptionUtil;
import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SCRM RPC
 */
@Slf4j
@Component
public class ScrmRpc {

    /**
     * 测试域名
     */
    private static final String TEST_DOMAIN = "https://testscrm.okii.com";
    /**
     * 正式域名
     */
    private static final String PROD_DOMAIN = "https://scrm.okii.com";
    /**
     * 测试访问ID
     */
    private static final String TEST_ACCESS_ID = "c35b44235d78943b33d09cb7eeac90cb";
    /**
     * 测试访问密钥
     */
    private static final String TEST_ACCESS_SECRET = "a00e83a5dbf8b4245e7fe09031d53b36";
    /**
     * 正式访问ID
     */
    private static final String PROD_ACCESS_ID = "677489ad66c82f2b6780a9409958426a";
    /**
     * 正式访问密钥
     */
    private static final String PROD_ACCESS_SECRET = "b4307c35944984c2f857c3e3d23e58c5";

    /**
     * http 请求实例
     */
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    static {
        // 设置字符串的转换使用UTF-8编码
        REST_TEMPLATE.getMessageConverters().stream()
                .filter(converter -> converter.getClass().equals(StringHttpMessageConverter.class))
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));
        // 设置请求超时时间
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(3000);
        requestFactory.setReadTimeout(5000);
        REST_TEMPLATE.setRequestFactory(requestFactory);
    }

    @Value("${spring.profiles.active}")
    private String profileActive;

    /**
     * 设置环境配置（用于测试）
     *
     * @param profileActive 环境配置
     */
    public void setProfileActive(String profileActive) {
        this.profileActive = profileActive;
    }

    /**
     * 获取二级代理列表
     *
     * @param userId 用户ID
     * @return 二级代理列表
     */
    public List<GetSecondAgentResponse.SecondAgent> getSecondAgentList(String userId) {
        // 构建请求
        GetSecondAgentRequest request = new GetSecondAgentRequest();
        request.setUserId(userId);
        // 调用接口
        GetSecondAgentResponse response = this.call(request);
        return response.getData() != null ? response.getData() : List.of();
    }

    /**
     * 调用接口
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 响应
     */
    private <T extends BaseScrmResponse> T call(BaseScrmRequest<T> request) {
        // 初始化
        RequestEntity<?> requestEntity = this.createRequestEntity(request);
        String responseStr = "";
        String requestInfoLog = "SCRM RPC %s url: %s, header: %s, body: %s"
                .formatted(requestEntity.getMethod(), requestEntity.getUrl(), requestEntity.getHeaders(), requestEntity.getBody());
        try {
            // 发起请求
            log.info(requestInfoLog);
            ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(requestEntity, String.class);
            responseStr = responseEntity.getBody();
            log.info("{}, response: {}", requestInfoLog, responseStr);
            // 解析响应
            T response = GsonUtil.jsonToBean(responseStr, request.getResponseClass());
            if (response == null || response.failure()) {
                throw this.remoteException();
            }
            return response;
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            if (e instanceof HttpStatusCodeException) {
                responseStr = e.getMessage();
            }
            throw rpcSysException(responseStr, requestEntity, e);
        }
    }

    /**
     * 生成请求
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 请求
     */
    private <T extends BaseScrmResponse> RequestEntity<?> createRequestEntity(BaseScrmRequest<T> request) {
        // 设置默认值
        boolean isTestProfile = SystemConstant.isTestProfile(profileActive);
        if (StringUtils.isBlank(request.getDomain())) {
            request.setDomain(isTestProfile ? TEST_DOMAIN : PROD_DOMAIN);
        }
        if (StringUtils.isBlank(request.getAccessId())) {
            request.setAccessId(isTestProfile ? TEST_ACCESS_ID : PROD_ACCESS_ID);
        }
        if (StringUtils.isBlank(request.getAccessSecret())) {
            request.setAccessSecret(isTestProfile ? TEST_ACCESS_SECRET : PROD_ACCESS_SECRET);
        }

        // 生成加密参数
        SortedMap<String, Object> extraParams = new TreeMap<>();
        // 如果是GetSecondAgentRequest，添加userId参数
        if (request instanceof GetSecondAgentRequest getSecondAgentRequest && StringUtils.isNotBlank(getSecondAgentRequest.getUserId())) {
            extraParams.put("userId", getSecondAgentRequest.getUserId());
        }

        ScrmEncryptionUtil.EncryptionParams encryptionParams = ScrmEncryptionUtil.createEncryptionParams(
                request.getAccessId(), request.getAccessSecret(), extraParams);

        request.setTs(encryptionParams.getTs());
        request.setNonce(encryptionParams.getNonce());
        request.setSignature(encryptionParams.getSignature());

        // 构建请求URL
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(request.getFullRequestUrl());

        // GET 请求参数添加到 URL 中
        if (request.getRequestMethod() == HttpMethod.GET) {
            String requestJson = GsonUtil.objectToJson(request);
            Map<String, Object> requestMap = GsonUtil.jsonToMap(requestJson);
            requestMap.forEach((key, value) -> uriBuilder.queryParamIfPresent(key, Optional.ofNullable(value)));
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // POST 请求参数添加到 body，GET 请求无 body
        RequestEntity.BodyBuilder requestBuilder = RequestEntity.method(request.getRequestMethod(), uriBuilder.build().toUri()).headers(headers);
        return request.getRequestMethod() == HttpMethod.GET ? requestBuilder.build()
                : requestBuilder.body(GsonUtil.objectToJson(request));
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @param responseStr   响应结果
     * @param requestEntity 请求实体
     * @param e             异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, RequestEntity<?> requestEntity, Exception e) {
        String msg = "SCRM RPC异常 response: %s, %s url: %s".formatted(responseStr, requestEntity.getMethod(), requestEntity.getUrl());
        return SysException.of(SysErrorCode.S_RPC_ERROR, msg, e);
    }

    public static void main(String[] args) {
        ScrmRpc rpc = new ScrmRpc();
        rpc.profileActive = "test";
        List<GetSecondAgentResponse.SecondAgent> result = rpc.getSecondAgentList("259c15e9ad264b89bea3b2f002807a64");
        log.info("二级代理列表: {}", result);
    }

}
