package com.xtc.marketing.xtcmarketing.rpc.scrm.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * SCRM基础响应DTO
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public abstract class BaseScrmResponse {

    /**
     * 成功响应码
     */
    public static final String SUCCESS_CODE = "000001";

    /**
     * 响应码
     */
    @SerializedName("code")
    private String code;
    /**
     * 响应信息
     */
    @SerializedName("message")
    private String message;

    /**
     * 判断请求失败
     *
     * @return 执行结果
     */
    public boolean failure() {
        return !success();
    }

    /**
     * 判断请求成功
     *
     * @return 执行结果
     */
    public boolean success() {
        return SUCCESS_CODE.equals(code);
    }

}
