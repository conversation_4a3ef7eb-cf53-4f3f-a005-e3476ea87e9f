package com.xtc.marketing.xtcmarketing.rpc.scrm.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 获取二级代理响应DTO
 */
@Getter
@Setter
@ToString
public class GetSecondAgentResponse extends BaseScrmResponse<List<GetSecondAgentResponse.SecondAgent>> {

    /**
     * 二级代理信息
     */
    @Getter
    @Setter
    @ToString
    public static class SecondAgent {
        /**
         * ID
         */
        @SerializedName("id")
        private Long id;
        /**
         * 代理ID
         */
        @SerializedName("agentId")
        private String agentId;
        /**
         * 二级名称
         */
        @SerializedName("secondName")
        private String secondName;
        /**
         * 二级代理ID
         */
        @SerializedName("secondAgentId")
        private String secondAgentId;
        /**
         * 备注
         */
        @SerializedName("remark")
        private String remark;
        /**
         * 公共二级代理ID
         */
        @SerializedName("publicSecondAgentId")
        private String publicSecondAgentId;
    }

}
