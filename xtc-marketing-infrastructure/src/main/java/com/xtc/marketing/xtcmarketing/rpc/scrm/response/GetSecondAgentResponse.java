package com.xtc.marketing.xtcmarketing.rpc.scrm.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 获取二级代理响应DTO
 */
@Getter
@Setter
@ToString
public class GetSecondAgentResponse extends BaseScrmResponse {

    /**
     * 响应数据
     */
    @SerializedName("data")
    private List<SecondAgent> data;

    /**
     * 二级代理信息
     */
    @Getter
    @Setter
    @ToString
    public static class SecondAgent {
        /**
         * 代理ID
         */
        @SerializedName("agentId")
        private String agentId;
        /**
         * 代理名称
         */
        @SerializedName("agentName")
        private String agentName;
        /**
         * 代理代码
         */
        @SerializedName("agentCode")
        private String agentCode;
        /**
         * 用户ID
         */
        @SerializedName("userId")
        private String userId;
        /**
         * 用户名称
         */
        @SerializedName("userName")
        private String userName;
        /**
         * 状态
         */
        @SerializedName("status")
        private Integer status;
        /**
         * 创建时间
         */
        @SerializedName("createTime")
        private String createTime;
        /**
         * 更新时间
         */
        @SerializedName("updateTime")
        private String updateTime;
    }

}
