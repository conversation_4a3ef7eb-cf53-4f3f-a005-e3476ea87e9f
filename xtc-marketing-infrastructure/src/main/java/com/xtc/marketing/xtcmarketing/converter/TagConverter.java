package com.xtc.marketing.xtcmarketing.converter;

import com.xtc.marketing.xtcmarketing.dataobject.TagDO;
import com.xtc.marketing.xtcmarketing.dto.TagDTO;
import org.mapstruct.*;

import java.util.List;

/**
 * 标签数据转换器
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true),
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface TagConverter {

    TagDTO doToDto(TagDO source);

    List<TagDTO> doToDto(List<TagDO> source);

}
