package com.xtc.marketing.xtcmarketing.context;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 用户上下文存储
 */
public class UserContext {

    private static final ThreadLocal<User> USER = new ThreadLocal<>();

    private UserContext() {
    }

    public static User getUser() {
        return USER.get();
    }

    public static void setUser(User user) {
        USER.set(user);
    }

    public static void remove() {
        USER.remove();
    }

    @Getter
    @Setter
    @ToString
    @Builder
    public static class User {

        /**
         * 用户id
         */
        private String userId;
        /**
         * 用户名称
         */
        private String userName;

    }

}
