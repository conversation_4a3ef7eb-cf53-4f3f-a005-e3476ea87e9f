# SCRM RPC 使用说明

## 概述

ScrmRpc 是用于调用 SCRM 系统 API 的 RPC 客户端，支持自动签名和环境配置切换。

## 功能特性

1. **签名逻辑内置**：`BaseScrmRequest` 内置签名生成逻辑
2. **环境自动切换**：根据 `spring.profiles.active` 自动选择测试或正式环境配置
3. **标准化架构**：参考 `JinglingRpc` 和 `WechatRpc` 的实现模式
4. **泛型响应支持**：`BaseScrmResponse<T>` 支持 code、msg、data 三个字段

## 环境配置

### 测试环境
- 域名：`https://testscrm.okii.com`
- accessId：`c35b44235d78943b33d09cb7eeac90cb`
- accessSecret：`a00e83a5dbf8b4245e7fe09031d53b36`

### 正式环境
- 域名：`https://scrm.okii.com`
- accessId：`677489ad66c82f2b6780a9409958426a`
- accessSecret：`b4307c35944984c2f857c3e3d23e58c5`

## 使用示例

### 1. Spring 注入使用

```java
@Autowired
private ScrmRpc scrmRpc;

public void getAgents(String userId) {
    List<GetSecondAgentResponse.SecondAgent> agents = scrmRpc.getSecondAgentList(userId);
    // 处理代理列表
}
```

### 2. 手动测试

```java
ScrmRpc rpc = new ScrmRpc();
rpc.setProfileActive("test"); // 设置测试环境
List<GetSecondAgentResponse.SecondAgent> agents = rpc.getSecondAgentList("259c15e9ad264b89bea3b2f002807a64");
```

## 签名逻辑说明

签名逻辑在 `BaseScrmRequest.generateSignature()` 方法中实现：

1. **生成随机字符串**：使用 UUID 生成 5 位随机字符串作为 nonce
2. **获取时间戳**：使用 `DateUtil.nowEpochSecond()` 获取当前时间戳（秒）
3. **参数排序**：将所有参数按 key 排序后拼接
4. **MD5 加密**：对拼接字符串进行 MD5 加密并转为大写

## 响应格式

```json
{
  "code": "000001",
  "msg": "success",
  "data": [
    {
      "agentId": "代理ID",
      "agentName": "代理名称",
      "agentCode": "代理代码",
      "userId": "用户ID",
      "userName": "用户名称",
      "status": 1,
      "createTime": "创建时间",
      "updateTime": "更新时间"
    }
  ]
}
```

## 测试

运行 `ScrmRpc` 类的 main 方法进行测试：

```bash
java -cp ... com.xtc.marketing.xtcmarketing.rpc.scrm.ScrmRpc
```

## 扩展说明

如需添加新的 SCRM 接口，请按以下步骤：

1. 在 `request` 包下创建新的请求类，继承 `BaseScrmRequest`
2. 在 `response` 包下创建新的响应类，继承 `BaseScrmResponse<T>`
3. 在 `ScrmRpc` 类中添加对应的方法
4. 根据需要在 `createRequestEntity` 方法中添加特殊参数处理逻辑
