package com.xtc.marketing.xtcmarketing.dataobject;

import com.google.gson.annotations.SerializedName;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xtc.marketing.xtcmarketing.config.BaseDO;
import com.xtc.marketing.xtcmarketing.config.MyBatisFlexGsonTypeHandler;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店标签表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_shop_tag")
public class ShopTagDO extends BaseDO {

    /**
     * 门店id
     */
    private String shopId;
    /**
     * 标签集合
     */
    @Column(typeHandler = MyBatisFlexGsonTypeHandler.class)
    private List<Tag> tags;

    /**
     * 标签
     */
    @Getter
    @Setter
    @ToString
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Tag {

        /**
         * 标签类型
         */
        @SerializedName(value = "tagType", alternate = "tag_type")
        private String tagType;
        /**
         * 标签代码
         */
        @SerializedName(value = "tagCode", alternate = "tag_code")
        private String tagCode;
        /**
         * 标签名称
         */
        @SerializedName(value = "tagName", alternate = "tag_name")
        private String tagName;
        /**
         * 标签值
         */
        @SerializedName(value = "tagValue", alternate = "tag_value")
        private String tagValue;
        /**
         * 标签时间
         */
        @SerializedName(value = "tagTime", alternate = "tag_time")
        private LocalDateTime tagTime;
        /**
         * 标签序号
         */
        @SerializedName(value = "tagNo", alternate = "tag_no")
        private Integer tagNo;

    }

}
