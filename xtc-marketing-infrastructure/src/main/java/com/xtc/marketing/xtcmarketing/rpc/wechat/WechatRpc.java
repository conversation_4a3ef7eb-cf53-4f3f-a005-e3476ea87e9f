package com.xtc.marketing.xtcmarketing.rpc.wechat;

import com.xtc.marketing.xtcmarketing.constant.SystemConstant;
import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.rpc.wechat.request.BaseWechatRequest;
import com.xtc.marketing.xtcmarketing.rpc.wechat.request.GetUserInfoRequest;
import com.xtc.marketing.xtcmarketing.rpc.wechat.response.BaseWechatResponse;
import com.xtc.marketing.xtcmarketing.rpc.wechat.response.GetUserInfoResponse;
import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import com.xtc.marketing.xtcmarketing.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Optional;

/**
 * 微信RPC
 */
@Slf4j
@Component
public class WechatRpc {

    /**
     * http 请求实例
     */
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    static {
        // 设置字符串的转换使用UTF-8编码
        REST_TEMPLATE.getMessageConverters().stream()
                .filter(converter -> converter.getClass().equals(StringHttpMessageConverter.class))
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));
        // 设置请求超时时间
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(3000);
        requestFactory.setReadTimeout(3000);
        REST_TEMPLATE.setRequestFactory(requestFactory);
    }

    @Value("${spring.profiles.active}")
    private String profileActive;

    /**
     * 获取企业微信用户id
     *
     * @param scrmWechatId 营销接口，微信id
     * @param code         临时登录凭证 code
     * @return 企业微信用户id
     */
    public Optional<String> getUserId(String scrmWechatId, String code) {
        // 构建请求
        GetUserInfoRequest request = new GetUserInfoRequest();
        request.setWechatId(scrmWechatId);
        request.setCode(code);
        // 调用接口
        GetUserInfoResponse response = this.call(request);
        return Optional.ofNullable(response.getUserId());
    }

    /**
     * 调用接口
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 响应
     */
    private <T extends BaseWechatResponse> T call(BaseWechatRequest<T> request) {
        // 初始化
        RequestEntity<?> requestEntity = this.createRequestEntity(request);
        String responseStr = "";
        String requestInfoLog = "微信RPC %s url: %s, header: %s, body: %s"
                .formatted(requestEntity.getMethod(), requestEntity.getUrl(), requestEntity.getHeaders(), requestEntity.getBody());
        try {
            // 发起请求
            log.info(requestInfoLog);
            ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(requestEntity, String.class);
            responseStr = responseEntity.getBody();
            log.info("{}, response: {}", requestInfoLog, responseStr);
            // 解析响应
            T response = GsonUtil.jsonToBean(responseStr, request.getResponseClass());
            if (response == null || response.failure()) {
                throw this.remoteException();
            }
            return response;
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            if (e instanceof HttpStatusCodeException) {
                responseStr = e.getMessage();
            }
            throw rpcSysException(responseStr, requestEntity, e);
        }
    }

    /**
     * 生成请求
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 请求
     */
    private <T extends BaseWechatResponse> RequestEntity<?> createRequestEntity(BaseWechatRequest<T> request) {
        // 获取 accessToken
        if (StringUtils.isBlank(request.getAccessToken()) || StringUtils.isNotBlank(request.getWechatId())) {
            String accessToken = this.getAccessToken(request.getWechatId())
                    .orElseThrow(() -> rpcSysException("获取 access_token 失败"));
            request.setAccessToken(accessToken);
        }
        // 构建请求URL，并添加 access_token 参数
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(request.getRequestUrl())
                .queryParam("access_token", request.getAccessToken());
        // GET 请求参数添加到 URL 中
        if (request.getRequestMethod() == HttpMethod.GET) {
            String requestJson = GsonUtil.objectToJson(request);
            Map<String, Object> requestMap = GsonUtil.jsonToMap(requestJson);
            requestMap.forEach((key, value) -> uriBuilder.queryParamIfPresent(key, Optional.ofNullable(value)));
        }
        // POST 请求参数添加到 body
        RequestEntity.BodyBuilder requestBuilder = RequestEntity.method(request.getRequestMethod(), uriBuilder.build().toUri());
        return request.getRequestMethod() == HttpMethod.GET ? requestBuilder.build()
                : requestBuilder.contentType(MediaType.APPLICATION_JSON).body(GsonUtil.objectToJson(request));
    }

    /**
     * 获取小程序全局唯一后台接口调用凭据（access_token）
     * 调用公共服务项目接口获取 <a href="https://xtc-api-doc.okii.com/#/view/kXO6x98D">文档</a>
     *
     * @param scrmWechatId 营销接口，微信id
     * @return 接口调用凭据
     */
    private Optional<String> getAccessToken(String scrmWechatId) {
        String domain = SystemConstant.isTestProfile(profileActive) ? "https://scrmapis.okii.com"
                : "http://scrm-api.marketing-component-env-prod:8080";
        String url = "%s/wechat/accessToken?wechatId=%s".formatted(domain, scrmWechatId);
        String response = HttpUtil.get(url);
        String accessToken = GsonUtil.getAsString(response, "data", "accessToken");
        return Optional.ofNullable(accessToken);
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @return 异常
     */
    private SysException rpcSysException(String message) {
        String msg = "微信RPC异常 message: %s".formatted(message);
        return SysException.of(SysErrorCode.S_RPC_ERROR, msg);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr   响应结果
     * @param requestEntity 请求实体
     * @param e             异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, RequestEntity<?> requestEntity, Exception e) {
        String msg = "微信RPC异常 response: %s, %s url: %s".formatted(responseStr, requestEntity.getMethod(), requestEntity.getUrl());
        return SysException.of(SysErrorCode.S_RPC_ERROR, msg, e);
    }

    public static void main(String[] args) {
        WechatRpc rpc = new WechatRpc();
        rpc.profileActive = "test";
        rpc.getUserId("shop-tag-test", "test-code")
                .ifPresent(userId -> log.info("获取到的用户ID: {}", userId));
    }

}
