package com.xtc.marketing.xtcmarketing.dataobject;

import com.mybatisflex.annotation.Table;
import com.xtc.marketing.xtcmarketing.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * 门店标签表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_tag")
public class TagDO extends BaseDO {

    /**
     * 标签序号
     */
    private Integer tagNo;
    /**
     * 标签类型
     */
    private String tagType;
    /**
     * 标签代码
     */
    private String tagCode;
    /**
     * 标签名称
     */
    private String tagName;
    /**
     * 标签值选项，多个值使用英文逗号分隔
     */
    private String tagValueOptions;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
