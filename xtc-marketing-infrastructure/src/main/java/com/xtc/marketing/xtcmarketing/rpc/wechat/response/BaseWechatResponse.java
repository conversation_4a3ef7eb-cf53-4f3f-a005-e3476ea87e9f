package com.xtc.marketing.xtcmarketing.rpc.wechat.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信基础响应DTO
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public abstract class BaseWechatResponse {

    /**
     * 成功响应码
     */
    public static final String SUCCESS_CODE = "0";

    /**
     * 错误响应码
     */
    @SerializedName("errcode")
    private String errCode;
    /**
     * 错误信息
     */
    @SerializedName("errmsg")
    private String errMsg;

    /**
     * 判断请求失败
     *
     * @return 执行结果
     */
    public boolean failure() {
        return !success();
    }

    /**
     * 判断请求成功
     *
     * @return 执行结果
     */
    public boolean success() {
        return SUCCESS_CODE.equals(errCode);
    }

}
