package com.xtc.marketing.xtcmarketing.dataobject;

import com.mybatisflex.annotation.Table;
import com.xtc.marketing.xtcmarketing.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * 用户表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_user")
public class UserDO extends BaseDO {

    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
