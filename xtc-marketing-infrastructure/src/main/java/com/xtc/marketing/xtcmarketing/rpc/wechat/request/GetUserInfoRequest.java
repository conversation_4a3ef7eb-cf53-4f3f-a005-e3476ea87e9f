package com.xtc.marketing.xtcmarketing.rpc.wechat.request;

import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.xtcmarketing.rpc.wechat.response.GetUserInfoResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpMethod;

/**
 * 企业微信用户信息DTO
 * <p><a href="http://developer.work.weixin.qq.com/document/path/96442">文档</a></p>
 */
@Getter
@Setter
@ToString
public class GetUserInfoRequest extends BaseWechatRequest<GetUserInfoResponse> {

    /**
     * 临时登录凭证 code
     */
    @SerializedName("code")
    private String code;

    @Override
    public HttpMethod getRequestMethod() {
        return HttpMethod.GET;
    }

    @Override
    public String getRequestUrl() {
        return "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo";
    }

    @Override
    public Class<GetUserInfoResponse> getResponseClass() {
        return GetUserInfoResponse.class;
    }

}
