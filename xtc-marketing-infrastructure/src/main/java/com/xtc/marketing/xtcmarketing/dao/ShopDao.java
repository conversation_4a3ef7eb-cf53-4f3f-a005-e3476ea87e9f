package com.xtc.marketing.xtcmarketing.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryCondition;
import com.xtc.marketing.xtcmarketing.config.BaseDao;
import com.xtc.marketing.xtcmarketing.dao.mapper.ShopMapper;
import com.xtc.marketing.xtcmarketing.dataobject.ShopDO;
import com.xtc.marketing.xtcmarketing.dto.query.ShopPageQry;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.xtcmarketing.dataobject.table.ShopDOTableDef.SHOP_DO;

/**
 * 门店数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class ShopDao extends BaseDao<ShopMapper, ShopDO> {

    /**
     * 查询门店
     *
     * @param shopId 门店id
     * @return 门店
     */
    public Optional<ShopDO> getByShopId(String shopId) {
        if (If.noText(shopId)) {
            return Optional.empty();
        }
        return queryChain()
                .where(SHOP_DO.SHOP_ID.eq(shopId))
                .orderBy(SHOP_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 查询门店分页列表
     *
     * @param qry 参数
     * @return 门店分页列表
     */
    public Page<ShopDO> pageBy(ShopPageQry qry) {
        Page<ShopDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(SHOP_DO.SHOP_ID.eq(qry.getShopId(), If::hasText))
                .and(
                        QueryCondition.createEmpty()
                                .and("MATCH (" + SHOP_DO.SHOP_NAME.getName() + ") AGAINST (? IN BOOLEAN MODE)", "+" + qry.getShopName())
                                .when(If.hasText(qry.getShopName()))
                )
                .orderBy(SHOP_DO.ID.desc())
                .page(page);
    }

}
