package com.xtc.marketing.xtcmarketing.rpc.scrm.request;

import com.google.gson.annotations.Expose;
import com.xtc.marketing.xtcmarketing.rpc.scrm.response.BaseScrmResponse;
import com.xtc.marketing.xtcmarketing.util.Md5Encoder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.http.HttpMethod;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * SCRM基础请求类
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public abstract class BaseScrmRequest<T extends BaseScrmResponse<?>> {

    /**
     * 默认时区偏移量
     */
    private static final ZoneOffset DEFAULT_ZONE_OFFSET = ZoneOffset.of("+8");

    /**
     * 域名
     */
    @Expose
    private String domain;
    /**
     * 访问ID
     */
    @Expose
    private String accessId;
    /**
     * 访问密钥
     */
    @Expose
    private String accessSecret;
    /**
     * 时间戳
     */
    @Expose
    private Long ts;
    /**
     * 随机字符串
     */
    @Expose
    private String nonce;
    /**
     * 签名
     */
    @Expose
    private String signature;

    /**
     * 获取 HTTP 请求方法
     *
     * @return HTTP 请求方法
     */
    public abstract HttpMethod getRequestMethod();

    /**
     * 获取接口路径
     *
     * @return 接口路径
     */
    public abstract String getRequestPath();

    /**
     * 获取 HTTP 响应类型
     *
     * @return 响应类型
     */
    public abstract Class<T> getResponseClass();

    /**
     * 获取完整请求URL
     *
     * @return 完整请求URL
     */
    public String getFullRequestUrl() {
        return domain + getRequestPath();
    }

    /**
     * 生成签名
     *
     * @param extraParams 额外参数
     */
    public void generateSignature(SortedMap<String, Object> extraParams) {
        // 生成随机字符串和时间戳
        this.nonce = RandomStringUtils.secure().randomAlphanumeric(5);
        this.ts = LocalDateTime.now().toEpochSecond(DEFAULT_ZONE_OFFSET);

        // 编排签名参数
        SortedMap<String, Object> signatureParams = new TreeMap<>();
        signatureParams.put("accessId", accessId);
        signatureParams.put("accessSecret", accessSecret);
        signatureParams.put("ts", ts);
        signatureParams.put("nonce", nonce);

        // 添加额外参数
        if (extraParams != null) {
            signatureParams.putAll(extraParams);
        }

        String signatureStr = signatureParams.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        // MD5加密，大写32位
        this.signature = Md5Encoder.encode(signatureStr).toUpperCase();
    }

    /**
     * 生成随机字符串
     *
     * @param length 长度
     * @return 随机字符串
     */
    private String generateNonce(int length) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid.substring(0, Math.min(length, uuid.length()));
    }

}
