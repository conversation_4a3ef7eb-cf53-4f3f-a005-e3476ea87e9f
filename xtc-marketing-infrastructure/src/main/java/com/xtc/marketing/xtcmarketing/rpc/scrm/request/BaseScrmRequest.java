package com.xtc.marketing.xtcmarketing.rpc.scrm.request;

import com.google.gson.annotations.Expose;
import com.xtc.marketing.xtcmarketing.rpc.scrm.response.BaseScrmResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.http.HttpMethod;

/**
 * SCRM基础请求类
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public abstract class BaseScrmRequest<T extends BaseScrmResponse> {

    /**
     * 域名
     */
    @Expose
    private String domain;
    /**
     * 访问ID
     */
    @Expose
    private String accessId;
    /**
     * 访问密钥
     */
    @Expose
    private String accessSecret;
    /**
     * 时间戳
     */
    @Expose
    private Long ts;
    /**
     * 随机字符串
     */
    @Expose
    private String nonce;
    /**
     * 签名
     */
    @Expose
    private String signature;

    /**
     * 获取 HTTP 请求方法
     *
     * @return HTTP 请求方法
     */
    public abstract HttpMethod getRequestMethod();

    /**
     * 获取接口路径
     *
     * @return 接口路径
     */
    public abstract String getRequestPath();

    /**
     * 获取 HTTP 响应类型
     *
     * @return 响应类型
     */
    public abstract Class<T> getResponseClass();

    /**
     * 获取完整请求URL
     *
     * @return 完整请求URL
     */
    public String getFullRequestUrl() {
        return domain + getRequestPath();
    }

}
