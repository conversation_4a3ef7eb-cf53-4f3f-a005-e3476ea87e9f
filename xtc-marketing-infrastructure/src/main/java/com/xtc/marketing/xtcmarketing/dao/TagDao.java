package com.xtc.marketing.xtcmarketing.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryCondition;
import com.xtc.marketing.xtcmarketing.config.BaseDao;
import com.xtc.marketing.xtcmarketing.dao.mapper.TagMapper;
import com.xtc.marketing.xtcmarketing.dataobject.TagDO;
import com.xtc.marketing.xtcmarketing.dto.query.TagPageQry;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static com.xtc.marketing.xtcmarketing.dataobject.table.TagDOTableDef.TAG_DO;

/**
 * 标签数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class TagDao extends BaseDao<TagMapper, TagDO> {

    /**
     * 根据标签代码查询标签
     *
     * @param tagCode 标签代码
     * @return 标签
     */
    public Optional<TagDO> getByTagCode(String tagCode) {
        if (If.noText(tagCode)) {
            return Optional.empty();
        }
        return queryChain()
                .where(TAG_DO.TAG_CODE.eq(tagCode))
                .orderBy(TAG_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 查询标签分页列表
     *
     * @param qry 参数
     * @return 标签分页列表
     */
    public Page<TagDO> pageBy(TagPageQry qry) {
        Page<TagDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(TAG_DO.TAG_NO.eq(qry.getTagNo(), If::notNull))
                .and(TAG_DO.TAG_TYPE.eq(qry.getTagType(), If::hasText))
                .and(TAG_DO.TAG_CODE.eq(qry.getTagCode(), If::hasText))
                .and(
                        QueryCondition.createEmpty()
                                .and("MATCH (" + TAG_DO.TAG_NAME.getName() + ") AGAINST (? IN BOOLEAN MODE)", "+" + qry.getTagName())
                                .when(If.hasText(qry.getTagName()))
                )
                .and(TAG_DO.ENABLED.eq(qry.getEnabled(), If::notNull))
                .orderBy(TAG_DO.ID.desc())
                .page(page);
    }

    /**
     * 判断标签序号是否存在
     *
     * @param tagNo 标签序号
     * @return 执行结果
     */
    public boolean existsByTagNo(Integer tagNo) {
        if (tagNo == null) {
            return false;
        }
        return queryChain().where(TAG_DO.TAG_NO.eq(tagNo)).exists();
    }

    /**
     * 判断标签代码是否存在
     *
     * @param tagCode 标签代码
     * @return 执行结果
     */
    public boolean existsByTagCode(String tagCode) {
        if (If.noText(tagCode)) {
            return false;
        }
        return queryChain().where(TAG_DO.TAG_CODE.eq(tagCode)).exists();
    }

    /**
     * 判断标签序号是否存在（排除指定ID）
     *
     * @param tagNo 标签序号
     * @param id    排除的ID
     * @return 执行结果
     */
    public boolean existsByTagNoExcludeId(Integer tagNo, Long id) {
        if (tagNo == null) {
            return false;
        }
        return queryChain()
                .where(TAG_DO.TAG_NO.eq(tagNo))
                .and(TAG_DO.ID.ne(id))
                .exists();
    }

    /**
     * 判断标签代码是否存在（排除指定ID）
     *
     * @param tagCode 标签代码
     * @param id      排除的ID
     * @return 执行结果
     */
    public boolean existsByTagCodeExcludeId(String tagCode, Long id) {
        if (If.noText(tagCode)) {
            return false;
        }
        return queryChain()
                .where(TAG_DO.TAG_CODE.eq(tagCode))
                .and(TAG_DO.ID.ne(id))
                .exists();
    }

    /**
     * 查询启用标签列表
     * <p>排序规则：标签类型顺序、更新时间倒序</p>
     *
     * @return 标签列表
     */
    public List<TagDO> listEnabled() {
        return queryChain()
                .where(TAG_DO.ENABLED.eq(Boolean.TRUE))
                .orderBy(TAG_DO.TAG_TYPE.asc(), TAG_DO.UPDATE_TIME.desc())
                .limit(LIMIT_LIST)
                .list();
    }

}
