package com.xtc.marketing.xtcmarketing.rpc.scrm.request;

import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.xtcmarketing.rpc.scrm.response.GetSecondAgentResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpMethod;

/**
 * 获取二级代理请求
 */
@Getter
@Setter
@ToString
public class GetSecondAgentRequest extends BaseScrmRequest<GetSecondAgentResponse> {

    /**
     * 用户ID
     */
    @SerializedName("userId")
    private String userId;

    @Override
    public HttpMethod getRequestMethod() {
        return HttpMethod.GET;
    }

    @Override
    public String getRequestPath() {
        return "/scrmadmin/open-api/secondAgent/getSecondAgentListByRecvUserId";
    }

    @Override
    public Class<GetSecondAgentResponse> getResponseClass() {
        return GetSecondAgentResponse.class;
    }

}
