package com.xtc.marketing.xtcmarketing.config;

import com.mybatisflex.annotation.InsertListener;
import com.mybatisflex.annotation.UpdateListener;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.dialect.IDialect;
import com.mybatisflex.core.logicdelete.LogicDeleteProcessor;
import com.mybatisflex.core.logicdelete.impl.BooleanLogicDeleteProcessor;
import com.mybatisflex.core.table.ColumnInfo;
import com.mybatisflex.core.table.TableInfo;
import com.xtc.marketing.xtcmarketing.context.UserContext;
import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

import static com.mybatisflex.core.constant.SqlConsts.EQUALS;

/**
 * MyBatis-Flex 全局配置
 */
@Configuration
public class MyBatisFlexConfig {

    MyBatisFlexConfig() {
        FlexGlobalConfig config = FlexGlobalConfig.getDefaultConfig();
        // 注册操作人数据监听器
        OperatorListener operatorListener = new OperatorListener();
        config.registerInsertListener(operatorListener, BaseDO.class);
        config.registerUpdateListener(operatorListener, BaseDO.class);
    }

    /**
     * 创建逻辑删除处理器
     * <p>记录删除人和删除时间</p>
     *
     * @return 逻辑删除处理器
     */
    @Bean
    public LogicDeleteProcessor logicDeleteProcessor() {
        return new BooleanLogicDeleteProcessor() {

            /**
             * 数据列：删除人
             */
            public static final String COLUMN_UPDATE_BY = "update_by";
            /**
             * 数据列：删除时间
             */
            public static final String COLUMN_UPDATE_TIME = "update_time";

            @Override
            public String buildLogicDeletedSet(String logicColumn, TableInfo tableInfo, IDialect dialect) {
                // 默认逻辑删除的 sql 语句
                String sql = dialect.wrap(logicColumn) + EQUALS + getLogicDeletedValue();
                // 获取当前用户
                UserContext.User user = UserContext.getUser();
                if (user == null) {
                    return sql;
                }
                // 扩展逻辑删除的 sql 语句，增加删除人和删除时间
                StringBuilder sqlBuilder = new StringBuilder(sql);
                for (ColumnInfo columnInfo : tableInfo.getColumnInfoList()) {
                    if (columnInfo.getColumn().equalsIgnoreCase(COLUMN_UPDATE_BY)) {
                        BaseDO.Operator operator = BaseDO.Operator.of(user.getUserId(), user.getUserName());
                        String updateBySql = ", %s %s '%s'".formatted(dialect.wrap(COLUMN_UPDATE_BY), EQUALS, GsonUtil.objectToJson(operator));
                        sqlBuilder.append(updateBySql);
                    }
                    if (columnInfo.getColumn().equalsIgnoreCase(COLUMN_UPDATE_TIME)) {
                        String updateTimeSql = ", %s %s now()".formatted(dialect.wrap(COLUMN_UPDATE_TIME), EQUALS);
                        sqlBuilder.append(updateTimeSql);
                    }
                }
                return sqlBuilder.toString();
            }
        };
    }

    /**
     * 操作人数据监听器，用于设置创建人和更新人
     */
    private static class OperatorListener implements InsertListener, UpdateListener {

        @Override
        public void onInsert(Object entity) {
            UserContext.User user = UserContext.getUser();
            if (user == null) {
                return;
            }
            BaseDO.Operator operator = BaseDO.Operator.of(user.getUserId(), user.getUserName());
            if (entity instanceof BaseDO baseDO) {
                LocalDateTime now = LocalDateTime.now();
                baseDO.setCreateBy(operator);
                baseDO.setCreateTime(now);
                baseDO.setUpdateBy(operator);
                baseDO.setUpdateTime(now);
            }
        }

        @Override
        public void onUpdate(Object entity) {
            UserContext.User user = UserContext.getUser();
            if (user == null) {
                return;
            }
            BaseDO.Operator operator = BaseDO.Operator.of(user.getUserId(), user.getUserName());
            if (entity instanceof BaseDO baseDO) {
                baseDO.setUpdateBy(operator);
                baseDO.setUpdateTime(LocalDateTime.now());
            }
        }

    }

}
