package com.xtc.marketing.xtcmarketing.config;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import com.mybatisflex.annotation.EnumValue;
import com.mybatisflex.core.handler.BaseJsonTypeHandler;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Optional;

/**
 * MyBatis-Flex Json 类型处理器 Gson
 * <pre>{@code
 * // 使用示例
 * @Column(typeHandler = MyBatisFlexGsonTypeHandler.class)
 * private BaseDO.Operator createBy;
 * }</pre>
 */
public class MyBatisFlexGsonTypeHandler extends BaseJsonTypeHandler<Object> {

    private static final String FORMAT_DATE = "yyyy-MM-dd";
    private static final String FORMAT_DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    private static Gson gson;
    private final Class<?> propertyType;
    private Class<?> genericType;

    public MyBatisFlexGsonTypeHandler(Class<?> propertyType) {
        this.propertyType = propertyType;
    }

    public MyBatisFlexGsonTypeHandler(Class<?> propertyType, Class<?> genericType) {
        this.propertyType = propertyType;
        this.genericType = genericType;
    }

    @Override
    protected Object parseJson(String json) {
        if (genericType != null) {
            TypeToken<?> typeToken = TypeToken.getParameterized(propertyType, genericType);
            return getGson().fromJson(json, typeToken);
        } else {
            return getGson().fromJson(json, propertyType);
        }
    }

    @Override
    protected String toJson(Object object) {
        return getGson().toJson(object);
    }

    private static Gson getGson() {
        if (gson == null) {
            gson = new GsonBuilder()
                    // 序列化
                    .registerTypeAdapter(LocalDateTime.class, (JsonSerializer<LocalDateTime>) (localDateTime, type, serializationContext) ->
                            new JsonPrimitive(localDateTime.format(DateTimeFormatter.ofPattern(FORMAT_DATE_TIME))))
                    .registerTypeAdapter(LocalDate.class, (JsonSerializer<LocalDate>) (localDate, type, serializationContext) ->
                            new JsonPrimitive(localDate.format(DateTimeFormatter.ofPattern(FORMAT_DATE))))
                    .registerTypeAdapter(DateFormat.class, (JsonSerializer<DateFormat>) (dateFormat, type, serializationContext) -> null)
                    // 反序化
                    .registerTypeAdapter(LocalDateTime.class, (JsonDeserializer<LocalDateTime>) (jsonElement, type, deserializationContext) ->
                            LocalDateTime.parse(jsonElement.getAsJsonPrimitive().getAsString(), DateTimeFormatter.ofPattern(FORMAT_DATE_TIME)))
                    .registerTypeAdapter(LocalDate.class, (JsonDeserializer<LocalDate>) (jsonElement, type, deserializationContext) ->
                            LocalDate.parse(jsonElement.getAsJsonPrimitive().getAsString(), DateTimeFormatter.ofPattern(FORMAT_DATE)))
                    .registerTypeAdapter(DateFormat.class, (JsonDeserializer<DateFormat>) (jsonElement, type, deserializationContext) -> null)
                    // 序列化数值类型时，整数不带小数点
                    .registerTypeAdapter(Integer.class, numberSerializer())
                    .registerTypeAdapter(Long.class, numberSerializer())
                    .registerTypeAdapter(Double.class, numberSerializer())
                    // 添加枚举类型适配器，使用 @EnumValue 注解的字段
                    .registerTypeAdapterFactory(new EnumTypeAdapterFactory())
                    .serializeNulls()
                    .disableHtmlEscaping()
                    .create();
        }
        return gson;
    }

    /**
     * 枚举类型适配器工厂，处理带有 @EnumValue 注解的枚举
     */
    private static class EnumTypeAdapterFactory implements TypeAdapterFactory {

        @SuppressWarnings("unchecked")
        @Override
        public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> typeToken) {
            Class<? super T> rawType = typeToken.getRawType();
            if (!Enum.class.isAssignableFrom(rawType) || rawType == Enum.class) {
                return null;
            }

            return new TypeAdapter<>() {

                @Override
                public void write(JsonWriter out, T value) throws IOException {
                    if (value == null) {
                        out.nullValue();
                        return;
                    }
                    // 查找带有 @EnumValue 注解的字段
                    Optional<Field> enumValueFieldOpt = Arrays.stream(rawType.getDeclaredFields())
                            .filter(field -> field.isAnnotationPresent(EnumValue.class))
                            .findFirst();
                    if (enumValueFieldOpt.isEmpty()) {
                        out.value(((Enum<?>) value).name());
                        return;
                    }
                    try {
                        // 获取 @EnumValue 注解的字段值
                        Object fieldValue = getFieldValue(value, enumValueFieldOpt.get());
                        gson.toJson(fieldValue, fieldValue.getClass(), out);
                    } catch (Exception e) {
                        // 如果没有找到 @EnumValue 注解的字段，则使用枚举名称
                        out.value(((Enum<?>) value).name());
                    }
                }

                @Override
                public T read(JsonReader in) throws IOException {
                    if (in.peek() == JsonToken.NULL) {
                        in.nextNull();
                        return null;
                    }
                    // 当前 json 值
                    JsonElement jsonElement = JsonParser.parseReader(in);
                    String enumValue = jsonElement.getAsString();
                    // 获取枚举所有实例
                    Enum<?>[] enumConstants = ((Class<Enum<?>>) rawType).getEnumConstants();
                    // 设置默认值，如果没有找到匹配的值，尝试通过名称匹配
                    T value = (T) Arrays.stream(enumConstants)
                            .filter(enumConstant -> enumConstant.name().equals(enumValue))
                            .findFirst()
                            .orElse(null);
                    // 查找带有 @EnumValue 注解的字段
                    Optional<Field> enumValueFieldOpt = Arrays.stream(rawType.getDeclaredFields())
                            .filter(field -> field.isAnnotationPresent(EnumValue.class))
                            .findFirst();
                    if (enumValueFieldOpt.isEmpty()) {
                        return value;
                    }
                    Field field = enumValueFieldOpt.get();
                    // 遍历枚举实例，找到匹配 json 的值
                    return Arrays.stream(enumConstants)
                            .filter(enumConstant -> equalsBetweenJsonAndObjectField(jsonElement, enumConstant, field))
                            .findFirst()
                            .map(enumConstant -> (T) enumConstant)
                            .orElse(value);
                }
            };
        }

        /**
         * 判断 json 值和实例的字段值相等
         *
         * @param jsonElement json 值
         * @param instance    实例
         * @param field       字段
         * @return 执行结果
         */
        private boolean equalsBetweenJsonAndObjectField(JsonElement jsonElement, Object instance, Field field) {
            if (!jsonElement.isJsonPrimitive()) {
                return false;
            }
            Object fieldValue;
            try {
                fieldValue = getFieldValue(instance, field);
            } catch (ReflectiveOperationException e) {
                return false;
            }
            JsonPrimitive primitive = jsonElement.getAsJsonPrimitive();
            return switch (fieldValue) {
                case Integer intValue when primitive.isNumber() -> primitive.getAsInt() == intValue;
                case Long longValue when primitive.isNumber() -> primitive.getAsLong() == longValue;
                case Double doubleValue when primitive.isNumber() -> primitive.getAsDouble() == doubleValue;
                case Float floatValue when primitive.isNumber() -> primitive.getAsFloat() == floatValue;
                case String str when primitive.isString() -> primitive.getAsString().equals(str);
                case Boolean bool when primitive.isBoolean() -> primitive.getAsBoolean() == bool;
                default -> false;
            };
        }

        /**
         * 获取实例的字段值
         *
         * @param instance 实例
         * @param field    字段
         * @return 字段值
         * @throws ReflectiveOperationException 反射异常
         */
        private Object getFieldValue(Object instance, Field field) throws ReflectiveOperationException {
            // key：属性名称，value：属性 getXxx 方法的返回值
            String fieldName = field.getName();
            // 获取属性的 getXxx 方法并执行
            String methodName = "get" + field.getName().substring(0, 1).toUpperCase() + fieldName.substring(1);
            Method method = instance.getClass().getMethod(methodName);
            return method.invoke(instance);
        }

    }

    /**
     * 序列化数值类型时，整数不带小数点
     *
     * @param <T> 数值类型
     * @return JsonSerializer
     */
    private static <T extends Number> JsonSerializer<T> numberSerializer() {
        return (number, type, context) -> {
            if (number instanceof Integer) {
                return new JsonPrimitive(number.intValue());
            }
            if (number instanceof Long) {
                return new JsonPrimitive(number.longValue());
            }
            if (number instanceof Double) {
                // 整数部分和原值（包含小数）相等时，返回整数部分，不包含小数
                long longValue = number.longValue();
                double doubleValue = number.doubleValue();
                if (longValue == doubleValue) {
                    return new JsonPrimitive(longValue);
                }
            }
            return new JsonPrimitive(number);
        };
    }

}
