package com.xtc.marketing.xtcmarketing.converter;

import com.xtc.marketing.xtcmarketing.dataobject.ShopDO;
import com.xtc.marketing.xtcmarketing.dataobject.ShopTagDO;
import com.xtc.marketing.xtcmarketing.dto.ShopDTO;
import org.mapstruct.*;

/**
 * 门店数据转换器
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true),
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface ShopConverter {

    @Mapping(target = "shopId", source = "shop.shopId")
    @Mapping(target = "tags", source = "shopTag.tags")
    @Mapping(target = "updateTime", source = "shopTag.updateTime")
    ShopDTO doToDto(ShopDO shop, ShopTagDO shopTag);

    ShopDTO.TagDTO tagToTagDto(ShopTagDO.Tag source);

}
