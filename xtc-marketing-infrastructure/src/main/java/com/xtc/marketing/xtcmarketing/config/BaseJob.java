package com.xtc.marketing.xtcmarketing.config;

import com.xtc.marketing.xtcmarketing.constant.SystemConstant;
import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.IntConsumer;

/**
 * 任务基类
 */
@Slf4j
public abstract class BaseJob {

    /**
     * 执行分片任务
     *
     * @param shardJob 分片任务（参数：分片索引）
     */
    public void executeShard(IntConsumer shardJob) {
        this.checkShard(XxlJobHelper.getShardTotal());
        this.execute(() -> shardJob.accept(XxlJobHelper.getShardIndex()));
    }

    /**
     * 执行分片任务
     *
     * @param shardJob 分片任务（参数：分片索引，分片总数）
     */
    public void executeShard(BiConsumer<Integer, Integer> shardJob) {
        this.checkShard(XxlJobHelper.getShardTotal());
        this.execute(() -> shardJob.accept(XxlJobHelper.getShardIndex(), XxlJobHelper.getShardTotal()));
    }

    /**
     * 执行分片任务
     *
     * @param clazz 参数类型
     * @param job   任务（入参：分片索引，任务参数）
     * @param <T>   参数类型
     */
    public <T> void executeShardWithParam(Class<T> clazz, BiConsumer<Integer, T> job) {
        execute(() -> {
            this.checkShard(XxlJobHelper.getShardTotal());
            String jobParam = XxlJobHelper.getJobParam();
            this.log("job param: {}", jobParam);
            T param = this.convertJobParam(jobParam, clazz);
            job.accept(XxlJobHelper.getShardIndex(), param);
        });
    }

    /**
     * 执行任务
     *
     * @param clazz 参数类型
     * @param job   任务（入参：任务参数）
     * @param <T>   参数类型
     */
    public <T> void executeWithParam(Class<T> clazz, Consumer<T> job) {
        execute(() -> {
            String jobParam = XxlJobHelper.getJobParam();
            this.log("job param: {}", jobParam);
            T param = this.convertJobParam(jobParam, clazz);
            job.accept(param);
        });
    }

    /**
     * 执行任务
     *
     * @param job 任务
     */
    public void execute(Runnable job) {
        // 生成日志id
        String traceId = UUID.randomUUID().toString().replace("-", "");
        MDC.put(SystemConstant.MDC_TRACE_ID, traceId);
        this.log("put trace.id: {}", traceId);
        // 打印任务信息
        LocalDateTime startTime = LocalDateTime.now();
        String jobName = Arrays.stream(Thread.currentThread().getStackTrace(), 2, 4)
                .filter(stack -> stack.getClassName().equals(this.getClass().getName()))
                .findFirst()
                .map(StackTraceElement::getMethodName)
                .orElse("unknown job name");
        this.log("job start: {}", jobName);
        // 执行任务，捕获异常输出异常日志
        try {
            job.run();
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            this.log("job failed: {}", jobName, e);
            XxlJobHelper.handleFail();
            XxlJobHelper.log(e);
        } finally {
            // 计算任务耗时
            LocalDateTime endTime = LocalDateTime.now();
            this.log("job end: {}, cost: {} ms", jobName, Duration.between(startTime, endTime).toMillis());
        }
    }

    /**
     * 打印日志
     *
     * @param format 日志格式
     * @param args   日志参数
     */
    public void log(String format, Object... args) {
        log.info(format, args);
        XxlJobHelper.log(format, args);
    }

    /**
     * 检查分片参数
     *
     * @param shardTotal 分片总数
     */
    private void checkShard(int shardTotal) {
        if (shardTotal == -1) {
            String msg = "分片参数错误，请将任务配置为分片模式";
            XxlJobHelper.handleFail(msg);
            throw SysException.of(SysErrorCode.S_JOB_ERROR, msg);
        }
    }

    /**
     * 转换任务参数
     *
     * @param jobParam 任务参数 json 字符串
     * @param clazz    参数类型
     * @param <T>      参数类型
     * @return 任务参数
     */
    private <T> T convertJobParam(String jobParam, Class<T> clazz) {
        T param = GsonUtil.jsonToBean(jobParam, clazz);
        if (param == null) {
            String msg = "任务参数为 null 任务执行失败";
            XxlJobHelper.handleFail(msg);
            throw SysException.of(SysErrorCode.S_JOB_ERROR, msg);
        }
        return param;
    }

}
