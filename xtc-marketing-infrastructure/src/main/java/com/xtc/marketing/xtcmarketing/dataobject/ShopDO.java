package com.xtc.marketing.xtcmarketing.dataobject;

import com.mybatisflex.annotation.Table;
import com.xtc.marketing.xtcmarketing.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 门店表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_out_scrm_webshop")
public class ShopDO extends BaseDO {

    /**
     * 门店id
     */
    private String shopId;
    /**
     * 门店名称
     */
    private String shopName;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 代理代码
     */
    private String agentCode;
    /**
     * 代理名称
     */
    private String agentName;
    /**
     * 二代id
     */
    private String secondAgentId;
    /**
     * 二代名称
     */
    private String secondAgentName;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 省市区编码
     */
    private String districtCode;
    /**
     * 售点类型
     */
    private String shopType;
    /**
     * 地址
     */
    private String address;
    /**
     * 经度坐标
     */
    private String longitude;
    /**
     * 纬度坐标
     */
    private String dimension;
    /**
     * 创建时间（业务字段）
     */
    private LocalDateTime createdTime;
    /**
     * 导购数
     */
    private Integer guideNum;

}
