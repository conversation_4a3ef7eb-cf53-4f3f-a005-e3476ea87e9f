package com.xtc.marketing.xtcmarketing;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.List;

/**
 * EnableAspectJAutoProxy 注解使用示例
 * <pre>{@code
 * // 获取当前类的代理类，从而实现调用自身方法时进行事务操作
 * UserAddCmdExe exe = (UserAddCmdExe) AopContext.currentProxy();
 * exe.transactionalFunction();
 * }</pre>
 * <p>配合多数据源插件一起使用时，需要在 @Transactional 注解配置 Propagation.REQUIRES_NEW。
 * <p>处理不了分布式事务，只能各自处理自己的事务。
 * <pre>{@code
 * // 方法必须是 public 修饰的才能生效
 * @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
 * public void transactionalFunction() {
 *     // do something
 * }
 * }</pre>
 */
@Slf4j
@EnableFeignClients(basePackages = "com.xtc")
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication(scanBasePackages = {"com.xtc.marketing.xtcmarketing", "com.alibaba.cola"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    /* cors configuration */
    @Value("${xtc.cors.allow-methods:}")
    private String allowMethods;
    @Value("${xtc.cors.allow-headers:}")
    private String allowHeaders;
    @Value("${xtc.cors.allow-origin-patterns:}")
    private String allowOriginPatterns;

    /* xxl job */
    @Value("${spring.application.name}")
    private String appName;
    @Value("${xxl.job.admin.addresses:}")
    private String xxlJobAddresses;
    @Value("${xxl.job.access-token:}")
    private String accessToken;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        if (StringUtils.isAnyBlank(appName, xxlJobAddresses)) {
            return null;
        }
        log.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(xxlJobAddresses);
        xxlJobSpringExecutor.setAppname(appName);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setPort(9999);
        xxlJobSpringExecutor.setLogPath("logs/xxl-job/jobhandler");
        xxlJobSpringExecutor.setLogRetentionDays(30);
        return xxlJobSpringExecutor;
    }

    /**
     * 跨域过滤器
     */
    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        if (StringUtils.isNotBlank(allowMethods)) {
            corsConfiguration.setAllowedMethods(List.of(allowMethods.split(",")));
        }
        if (StringUtils.isNotBlank(allowHeaders)) {
            corsConfiguration.setAllowedHeaders(List.of(allowHeaders.split(",")));
        }
        if (StringUtils.isNotBlank(allowOriginPatterns)) {
            corsConfiguration.setAllowedOriginPatterns(List.of(allowOriginPatterns.split(",")));
        }
        source.registerCorsConfiguration("/**", corsConfiguration);
        return new CorsFilter(source);
    }

}
