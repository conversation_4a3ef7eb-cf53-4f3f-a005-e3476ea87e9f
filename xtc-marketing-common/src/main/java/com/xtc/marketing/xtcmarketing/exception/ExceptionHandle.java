package com.xtc.marketing.xtcmarketing.exception;

import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import feign.FeignException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

/**
 * 异常处理类
 */
@Slf4j
@RestControllerAdvice
public class ExceptionHandle {

    /**
     * 未知异常处理
     */
    @ExceptionHandler({Exception.class})
    public ResponseEntity<ExceptionResponse> unknownExceptionHandler(Exception e) {
        log.error("UnknownException message: {}", e.getMessage(), e);
        return ResponseEntity.internalServerError()
                .body(ExceptionResponse.buildFailure(SysErrorCode.S_UNKNOWN_ERROR.getErrCode(),
                        SysErrorCode.S_UNKNOWN_ERROR.getErrDesc()));
    }

    /**
     * Feign 调用异常处理
     */
    @ExceptionHandler({FeignException.class})
    public ResponseEntity<ExceptionResponse> feignExceptionHandler(FeignException e) {
        log.error("FeignException message: {}", e.getMessage(), e);
        ExceptionResponse exceptionResponse = GsonUtil.jsonToBean(e.contentUTF8(), ExceptionResponse.class);
        return ResponseEntity.internalServerError().body(exceptionResponse);
    }

    /**
     * 系统异常处理
     */
    @ExceptionHandler({SysException.class})
    public ResponseEntity<ExceptionResponse> sysExceptionHandler(SysException e) {
        log.error("SysException message: {}", e.getMessage(), e);
        ExceptionResponse body = ExceptionResponse.buildFailure(e.getErrCode(), e.getErrDesc());
        if (SysErrorCode.S_PARAM_ERROR.getErrCode().equals(e.getErrCode())) {
            body.setErrMessage(e.getMessage());
        }
        return ResponseEntity.internalServerError().body(body);
    }

    /**
     * 业务异常处理
     */
    @ExceptionHandler({BizException.class})
    public ResponseEntity<ExceptionResponse> bizExceptionHandler(BizException e) {
        log.warn("BizException message: {}", e.getMessage(), e);
        return ResponseEntity.internalServerError()
                .body(ExceptionResponse.buildFailure(e.getErrCode(), e.getErrDesc()));
    }

    /**
     * 数据校验的处理
     */
    @ExceptionHandler({
            BindException.class,
            ConstraintViolationException.class,
            ServletRequestBindingException.class,
            TypeMismatchException.class
    })
    public ResponseEntity<ExceptionResponse> validatorExceptionHandler(Exception e) {
        String msg = switch (e) {
            // RequestBody 类校验异常
            case BindException bindException -> msgConvert(bindException);
            // java 包装类校验异常（String、Long、Double...）
            case ConstraintViolationException violationException -> msgConvert(violationException);
            default -> e.getMessage();
        };
        String exceptionMessage = SysErrorCode.S_PARAM_ERROR.getErrDesc() + "：" + msg;
        log.warn(exceptionMessage, e);
        return ResponseEntity.internalServerError()
                .body(ExceptionResponse.buildFailure(SysErrorCode.S_PARAM_ERROR.getErrCode(), exceptionMessage));
    }

    /**
     * 校验消息转换拼接，对象校验
     */
    private String msgConvert(BindException bindException) {
        List<FieldError> fieldErrors = bindException.getBindingResult().getFieldErrors();
        FieldError error = fieldErrors.getFirst();
        return error.getField() + error.getDefaultMessage();
    }

    /**
     * 校验消息转换拼接，基本数据类型
     */
    private String msgConvert(ConstraintViolationException violationException) {
        ConstraintViolation<?> violation = violationException.getConstraintViolations().iterator().next();
        String[] name = violation.getPropertyPath().toString().split("\\.");
        return name[name.length - 1] + violation.getMessage();
    }

}
