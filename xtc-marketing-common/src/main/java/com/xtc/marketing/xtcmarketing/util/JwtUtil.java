package com.xtc.marketing.xtcmarketing.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.Verification;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.management.openmbean.InvalidKeyException;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

/**
 * jwt 工具类
 */
@Slf4j
public class JwtUtil {

    /**
     * jwt 存储在请求头 headers 里的名称
     */
    private static final String JWT_HEADER = "Authorization";
    /**
     * jwt 存储在请求头 headers 里的值的前缀
     */
    private static final String JWT_PREFIX = "Bearer ";
    /**
     * RSA 算法
     */
    private static final String ALGORITHM_RSA = "RSA";
    /**
     * RSA 密钥大小，通常为 2048 或 4096
     */
    private static final int RAS_KEY_SIZE = 2048;
    /**
     * claim：系统
     */
    private static final String CLAIM_SYSTEM = "azp";
    /**
     * claim：用户id
     */
    private static final String CLAIM_USER_ID = "user_id";
    /**
     * 默认时区偏移量：UTC+8（时间相关数据使用）
     */
    private static final ZoneOffset DEFAULT_ZONE_OFFSET = ZoneOffset.of("+8");

    /* 本地缓存数据 */
    /**
     * 公钥
     */
    private static RSAPublicKey rsaPublicKey;
    /**
     * 私钥
     */
    private static RSAPrivateKey rsaPrivateKey;

    private JwtUtil() {
    }

    /**
     * 获取请求头 headers 里的 jwt 令牌
     *
     * @param request 请求
     * @return jwt 令牌
     */
    public static Optional<String> getJwt(HttpServletRequest request) {
        Enumeration<String> requestHeader = request.getHeaderNames();
        while (requestHeader.hasMoreElements()) {
            String headerKey = requestHeader.nextElement();
            if (JWT_HEADER.equalsIgnoreCase(headerKey)) {
                String jwt = request.getHeader(headerKey);
                if (StringUtils.isNotBlank(jwt)) {
                    jwt = jwt.replace(JWT_PREFIX, "");
                    return StringUtils.isNotBlank(jwt) ? Optional.of(jwt) : Optional.empty();
                }
            }
        }
        return Optional.empty();
    }

    /**
     * 生成 jwt 令牌
     *
     * @param base64PrivateKey 私钥 base64 字符串
     * @param issuer           发行方
     * @param system           系统
     * @param expiresAt        过期时间
     * @param userId           用户id
     * @return jwt 令牌
     */
    public static String generateToken(String base64PrivateKey, String issuer, String system, Duration expiresAt, String userId)
            throws NoSuchAlgorithmException, InvalidKeySpecException {
        RSAPrivateKey rsaPrivateKey = getRsaPrivateKey(base64PrivateKey);
        Algorithm algorithm = Algorithm.RSA256(null, rsaPrivateKey);
        LocalDateTime now = LocalDateTime.now();
        return JWT.create()
                .withIssuer(issuer)
                .withClaim(CLAIM_SYSTEM, system)
                .withClaim(CLAIM_USER_ID, userId)
                .withExpiresAt(now.plus(expiresAt).toInstant(DEFAULT_ZONE_OFFSET))
                .withIssuedAt(now.toInstant(DEFAULT_ZONE_OFFSET))
                .withJWTId(UUID.randomUUID().toString())
                .sign(algorithm);
    }

    /**
     * 验证 jwt 并获取用户id
     *
     * @param base64PublicKey 公钥 base64 字符串
     * @param issuer          发行方
     * @param system          系统
     * @param jwt             jwt 令牌
     * @return 用户id
     */
    public static Optional<String> verifyAndGetUserId(String base64PublicKey, String issuer, String system, String jwt)
            throws NoSuchAlgorithmException, InvalidKeySpecException {
        RSAPublicKey publicKey = getRsaPublicKey(base64PublicKey);
        Algorithm algorithm = Algorithm.RSA256(publicKey, null);
        Verification verification = JWT.require(algorithm)
                .withIssuer(issuer)
                .withClaimPresence(CLAIM_USER_ID);
        if (system != null) {
            verification.withClaim(CLAIM_SYSTEM, system);
        }
        Map<String, Claim> claims = verification.build().verify(jwt).getClaims();
        Claim userId = claims.get(CLAIM_USER_ID);
        return Optional.ofNullable(userId).map(Claim::asString);
    }

    /**
     * 获取 RSA 公钥
     *
     * @param base64PublicKey 公钥 base64 字符串
     * @return RSA 公钥
     */
    private static RSAPublicKey getRsaPublicKey(String base64PublicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        if (rsaPublicKey == null) {
            rsaPublicKey = loadRsaPublicKey(base64PublicKey);
        }
        if (rsaPublicKey == null) {
            throw new InvalidKeyException("Failed to load RSA public key");
        }
        return rsaPublicKey;
    }

    /**
     * 获取 RSA 私钥
     *
     * @param base64PrivateKey 私钥 base64 字符串
     * @return RSA 私钥
     */
    private static RSAPrivateKey getRsaPrivateKey(String base64PrivateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        if (rsaPrivateKey == null) {
            rsaPrivateKey = loadRsaPrivateKey(base64PrivateKey);
        }
        if (rsaPrivateKey == null) {
            throw new InvalidKeyException("Failed to load RSA private key");
        }
        return rsaPrivateKey;
    }

    /**
     * 生成 RSA 密钥对
     *
     * @return RSA密钥对
     * @throws NoSuchAlgorithmException 如果RSA算法不可用
     */
    private static KeyPair generateRSAKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM_RSA);
        keyPairGenerator.initialize(RAS_KEY_SIZE);
        return keyPairGenerator.generateKeyPair();
    }

    /**
     * 生成密钥的 base64 字符串
     *
     * @param key 密钥（公钥、密钥）
     */
    private static String generateBase64Key(Key key) {
        byte[] encoded = key.getEncoded();
        return Base64.getEncoder().encodeToString(encoded);
    }

    /**
     * 加载 RSA 公钥
     *
     * @param base64PublicKey 公钥 base64 字符串
     * @return RSA公钥
     */
    private static RSAPublicKey loadRsaPublicKey(String base64PublicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] decoded = Base64.getDecoder().decode(base64PublicKey);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM_RSA);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decoded);
        return (RSAPublicKey) keyFactory.generatePublic(keySpec);
    }

    /**
     * 加载 RSA 私钥
     *
     * @param base64PrivateKey 私钥 base64 字符串
     * @return RSA私钥
     */
    private static RSAPrivateKey loadRsaPrivateKey(String base64PrivateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] decoded = Base64.getDecoder().decode(base64PrivateKey);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM_RSA);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decoded);
        return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
    }

    public static void main(String[] args) throws Exception {
        String issuer = "xtc";
        String system = "system";
        String userId = "modefang";
        // 生成密钥对
        KeyPair keyPair = generateRSAKeyPair();
        // 生成密钥对的 base64 字符串
        String base64PublicKey = generateBase64Key(keyPair.getPublic());
        log.info("-----publicKey-----\n{}", base64PublicKey);
        String base64PrivateKey = generateBase64Key(keyPair.getPrivate());
        log.info("-----privateKey-----\n{}", base64PrivateKey);
        // 生成JWT令牌
        String jwt = generateToken(base64PrivateKey, issuer, system, Duration.ofSeconds(5), userId);
        log.info("-----jwt-----\n{}", jwt);
        // 验证JWT令牌
        verifyAndGetUserId(base64PublicKey, issuer, system, jwt).ifPresent(id -> log.info("userId: {}", id));
        // JWT过期验证
        Thread.sleep(6000);
        try {
            verifyAndGetUserId(base64PublicKey, issuer, system, jwt);
        } catch (TokenExpiredException e) {
            log.warn("token 已过期", e);
        }
    }

}
