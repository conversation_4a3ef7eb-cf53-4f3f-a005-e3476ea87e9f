package com.xtc.marketing.xtcmarketing.constant;

/**
 * 系统常量
 */
public class SystemConstant {

    private SystemConstant() {
    }

    /**
     * 系统名称
     */
    public static final String SYSTEM_NAME = "xtc-marketing";
    /**
     * 测试环境配置
     */
    public static final String PROFILE_TEST = "dev|test";
    /**
     * 业务代码
     */
    public static final String BIZ_CODE = "";
    /**
     * 业务名称
     */
    public static final String BIZ_NAME = "";
    /**
     * 日志打印key：全链路跟踪id
     */
    public static final String MDC_TRACE_ID = "trace.id";
    /**
     * 日志打印key：执行id（默认用在多线程场景，标识当前线程的执行id）
     */
    public static final String MDC_EXECUTE_ID = "executeId";

    /**
     * 判断测试环境
     *
     * @param profileActive 激活的配置
     * @return 执行结果
     */
    public static boolean isTestProfile(String profileActive) {
        return SystemConstant.PROFILE_TEST.contains(profileActive);
    }

}
