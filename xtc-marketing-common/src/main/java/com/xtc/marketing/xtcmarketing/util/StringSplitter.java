package com.xtc.marketing.xtcmarketing.util;

import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

/**
 * 字符串分割工具类
 */
public class StringSplitter {

    private StringSplitter() {
    }

    /**
     * 默认分隔符
     */
    public static final String SEPARATOR = ",";

    /**
     * 分割字符串，默认使用英文逗号分隔
     *
     * @param str 字符串
     * @return 字符串列表，注意该列表不支持变更
     */
    public static List<String> split(String str) {
        return split(str, SEPARATOR);
    }

    /**
     * 分割字符串
     *
     * @param str       字符串
     * @param separator 分隔符
     * @return 字符串列表，注意该列表不支持变更
     */
    public static List<String> split(String str, String separator) {
        if (StringUtils.isBlank(str)) {
            return Collections.emptyList();
        }
        return Splitter.on(separator).omitEmptyStrings().trimResults().splitToList(str);
    }

    /**
     * 分割字符串，默认使用英文逗号分隔
     *
     * @param str 字符串
     * @return 字符串流
     */
    public static Stream<String> splitToStream(String str) {
        return splitToStream(str, SEPARATOR);
    }

    /**
     * 分割字符串
     *
     * @param str       字符串
     * @param separator 分隔符
     * @return 字符串流
     */
    public static Stream<String> splitToStream(String str, String separator) {
        if (StringUtils.isBlank(str)) {
            return Stream.empty();
        }
        return Splitter.on(separator).omitEmptyStrings().trimResults().splitToStream(str);
    }

}
