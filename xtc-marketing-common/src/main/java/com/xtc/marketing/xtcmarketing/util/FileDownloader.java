package com.xtc.marketing.xtcmarketing.util;

import lombok.Getter;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.InputStreamSource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件下载工具类
 */
public class FileDownloader {

    private FileDownloader() {
    }

    /**
     * 构建文件下载的响应实体
     *
     * @param file               文件
     * @param contentType        文件类型
     * @param contentDisposition 内容处置方式（直接打开或者下载）
     * @return 响应实体
     * @see org.springframework.http.ResponseEntity
     * @see org.springframework.core.io.Resource
     * @see org.springframework.http.MediaType
     */
    public static ResponseEntity<Resource> buildResourceResponseEntity(Resource file,
                                                                       MediaType contentType,
                                                                       ContentDisposition contentDisposition) {
        return buildResourceResponseEntity(file.getFilename(), contentType, contentDisposition, file);
    }

    /**
     * 构建文件下载的响应实体
     * <p>文件下载接口开发：</p>
     * <pre>{@code
     * public ResponseEntity<Resource> downloadFile(String fileName) {
     *     return FileDownloader.buildResourceResponseEntity(fileName, MediaType.APPLICATION_OCTET_STREAM,
     *             () -> marketingInvoiceOssClient.getObject(file.getObjectName()));
     * }
     * }</pre>
     *
     * @param fileName           文件名（包含后缀，例：file.pdf）
     * @param fileType           文件类型（文件后缀，例：pdf）
     * @param contentDisposition 内容处置方式（直接打开或者下载）
     * @param streamSource       文件流源（懒加载）
     * @return 响应实体
     * @see org.springframework.http.ResponseEntity
     * @see org.springframework.core.io.Resource
     * @see org.springframework.core.io.InputStreamSource
     * @see org.springframework.http.MediaType
     */
    public static ResponseEntity<Resource> buildResourceResponseEntity(String fileName,
                                                                       String fileType,
                                                                       ContentDisposition contentDisposition,
                                                                       InputStreamSource streamSource) {
        // 确认文件类型，默认使用 application/octet-stream
        MediaType contentType = switch (fileType) {
            case "pdf" -> MediaType.APPLICATION_PDF;
            case "xml" -> MediaType.APPLICATION_XML;
            default -> MediaType.APPLICATION_OCTET_STREAM;
        };
        return buildResourceResponseEntity(fileName, contentType, contentDisposition, streamSource);
    }

    /**
     * 构建文件下载的响应实体
     * <p>文件下载接口开发：</p>
     * <pre>{@code
     * public ResponseEntity<Resource> downloadFile(String fileName) {
     *     return FileDownloader.buildResourceResponseEntity(fileName, MediaType.APPLICATION_OCTET_STREAM,
     *             () -> marketingInvoiceOssClient.getObject(file.getObjectName()));
     * }
     * }</pre>
     *
     * @param fileName           文件名（包含后缀）
     * @param contentType        文件类型
     * @param contentDisposition 内容处置方式（直接打开或者下载）
     * @param streamSource       文件流源（懒加载）
     * @return 响应实体
     * @see org.springframework.http.ResponseEntity
     * @see org.springframework.core.io.Resource
     * @see org.springframework.core.io.InputStreamSource
     * @see org.springframework.http.MediaType
     */
    public static ResponseEntity<Resource> buildResourceResponseEntity(String fileName,
                                                                       MediaType contentType,
                                                                       ContentDisposition contentDisposition,
                                                                       InputStreamSource streamSource) {
        // 空格在浏览器中会被解析成 + 号，所以需要替换成 %20
        String encodeFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("+", "%20");
        String fileNameContentDisposition = contentDisposition.getValue() + ";filename*=utf-8''" + encodeFileName;
        // Spring 负责关闭流
        InputStreamResource resource = new InputStreamResource(streamSource);
        return ResponseEntity.ok()
                .header("Content-Disposition", fileNameContentDisposition)
                .header("Content-Type", contentType.toString())
                .body(resource);
    }

    /**
     * 内容处置方式
     */
    @Getter
    public enum ContentDisposition {
        /**
         * 下载
         */
        ATTACHMENT("attachment"),
        /**
         * 直接打开
         */
        INLINE("inline");

        private final String value;

        ContentDisposition(String value) {
            this.value = value;
        }
    }

}
