package com.xtc.marketing.xtcmarketing.exception;

import lombok.Getter;

/**
 * 错误码主要有3部分组成：类型+场景+自定义标识
 */
@Getter
public enum BizErrorCode {
    // 参数异常
//    P_USER_UserIdNotNull("P_USER_UserIdNotNull", "用户id不能为空"),

    // 标签
    B_TAG_TagNotExists("B_TAG_TagNotExists", "标签不存在"),
    B_TAG_TagNoAlreadyExists("B_TAG_TagNoAlreadyExists", "标签序号已存在"),
    B_TAG_TagCodeAlreadyExists("B_TAG_TagCodeAlreadyExists", "标签代码已存在"),

    // 门店
    B_SHOP_ShopNotExists("B_SHOP_ShopNotExists", "门店不存在"),
    ;

    private final String errCode;
    private final String errDesc;

    BizErrorCode(String errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }
}
