package com.xtc.marketing.xtcmarketing.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES密码工具类
 */
public class AesUtil {

    private static final String AES = "AES";
    private static final String CIPHER_ALGORITHM_ECB = "AES/ECB/PKCS5Padding";
    private static final Charset CHARSET = StandardCharsets.UTF_8;

    private AesUtil() {
    }

    /**
     * 加密
     *
     * @param secret    密钥（建议从配置文件中取值，不要写死在代码里面）
     * @param plainText 明文
     * @return 密文，编码：base64
     */
    public static String encrypt(String secret, String plainText) {
        try {
            Cipher cipher = generateCipher(Cipher.ENCRYPT_MODE, secret);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(CHARSET));
            return base64Encode(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("AesUtil encrypt error", e);
        }
    }

    /**
     * 解密
     *
     * @param secret        密钥（建议从配置文件中取值，不要写死在代码里面）
     * @param encryptedText 密文，编码：base64
     * @return 明文
     */
    public static String decrypt(String secret, String encryptedText) {
        try {
            Cipher cipher = generateCipher(Cipher.DECRYPT_MODE, secret);
            byte[] decodeBase64 = base64Decode(encryptedText);
            byte[] decryptedBytes = cipher.doFinal(decodeBase64);
            return new String(decryptedBytes, CHARSET);
        } catch (Exception e) {
            throw new RuntimeException("AesUtil decrypt error", e);
        }
    }

    /**
     * 生成密码器
     *
     * @param mode   密码器工作模式
     * @param secret 字符串密钥
     * @return 密码器
     */
    private static Cipher generateCipher(int mode, String secret) {
        try {
            byte[] secretByte = secret.getBytes(CHARSET);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretByte, AES);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_ECB);
            cipher.init(mode, secretKeySpec);
            return cipher;
        } catch (Exception e) {
            throw new RuntimeException("AesUtil generateCipher error", e);
        }
    }

    /**
     * base64 编码
     *
     * @param src 源数据
     * @return 编码字符串
     */
    private static String base64Encode(byte[] src) {
        return Base64.getEncoder().encodeToString(src);
    }

    /**
     * base64 解码
     *
     * @param base64 编码字符串
     * @return 源数据
     */
    private static byte[] base64Decode(String base64) {
        return Base64.getDecoder().decode(base64);
    }

    public static void main(String[] args) throws Exception {
        String secret = "FDSfsdnk2bn32341";
        String plainText = "hello world!";

        String encryptedText = encrypt(secret, plainText);
        System.out.println("Encrypted: " + encryptedText);

        String decryptedText = decrypt(secret, encryptedText);
        System.out.println("Decrypted: " + decryptedText);
        System.out.println("plainText: " + plainText);
    }

}
