package com.xtc.marketing.xtcmarketing.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 标签编辑参数
 */
@Getter
@Setter
@ToString
public class TagEditCmd {

    /**
     * 标签序号
     */
    private Integer tagNo;
    /**
     * 标签类型
     */
    @Length(max = 20)
    private String tagType;
    /**
     * 标签代码
     */
    @Length(max = 50)
    private String tagCode;
    /**
     * 标签名称
     */
    @Length(max = 50)
    private String tagName;
    /**
     * 标签值选项，多个值使用英文逗号分隔
     */
    @Length(max = 200)
    private String tagValueOptions;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
