package com.xtc.marketing.xtcmarketing.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 用户编辑参数
 */
@Getter
@Setter
@ToString
public class UserEditCmd {

    /**
     * 用户id
     */
    @Length(max = 50)
    private String userId;
    /**
     * 用户名称
     */
    @Length(max = 50)
    private String userName;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
