package com.xtc.marketing.xtcmarketing.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 基础DTO
 */
@Getter
@Setter
@ToString
public class BaseDTO {

    /**
     * 唯一标识
     */
    private Long id;
    /**
     * 更新人
     */
    private OperatorDTO updateBy;
    /**
     * 创建人
     */
    private OperatorDTO createBy;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作人DTO
     */
    @Getter
    @Setter
    @ToString
    public static class OperatorDTO {

        /**
         * 操作人代码
         */
        private String code;
        /**
         * 操作人名称
         */
        private String name;

    }

}
