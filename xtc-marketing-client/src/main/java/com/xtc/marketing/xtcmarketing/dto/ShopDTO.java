package com.xtc.marketing.xtcmarketing.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店DTO
 */
@Getter
@Setter
@ToString
public class ShopDTO {

    /**
     * 门店id
     */
    private String shopId;
    /**
     * 门店名称
     */
    private String shopName;
    /**
     * 标签集合
     */
    private List<TagDTO> tags;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 标签DTO
     */
    @Getter
    @Setter
    @ToString
    public static class TagDTO {

        /**
         * 标签类型
         */
        private String tagType;
        /**
         * 标签代码
         */
        private String tagCode;
        /**
         * 标签名称
         */
        private String tagName;
        /**
         * 标签值
         */
        private String tagValue;
        /**
         * 标签时间
         */
        private LocalDateTime tagTime;
        /**
         * 标签序号
         */
        private Integer tagNo;

    }

}
