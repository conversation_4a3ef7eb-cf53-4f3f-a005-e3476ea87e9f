package com.xtc.marketing.xtcmarketing.dto.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 标签新增参数
 */
@Getter
@Setter
@ToString
public class TagCreateCmd {

    /**
     * 标签序号
     */
    @NotNull
    private Integer tagNo;
    /**
     * 标签类型
     */
    @NotBlank
    @Length(max = 20)
    private String tagType;
    /**
     * 标签代码
     */
    @NotBlank
    @Length(max = 50)
    private String tagCode;
    /**
     * 标签名称
     */
    @NotBlank
    @Length(max = 50)
    private String tagName;
    /**
     * 标签值选项，多个值使用英文逗号分隔
     */
    @Length(max = 200)
    private String tagValueOptions;
    /**
     * 启用标识
     */
    @NotNull
    private Boolean enabled;

}
