package com.xtc.marketing.xtcmarketing.dto.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 用户新增参数
 */
@Getter
@Setter
@ToString
public class UserCreateCmd {

    /**
     * 用户id
     */
    @NotBlank
    @Length(max = 50)
    private String userId;
    /**
     * 用户名称
     */
    @NotBlank
    @Length(max = 50)
    private String userName;
    /**
     * 启用标识
     */
    @NotNull
    private Boolean enabled;

}
