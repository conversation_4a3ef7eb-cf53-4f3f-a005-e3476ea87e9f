package com.xtc.marketing.xtcmarketing.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 门店标签
 */
@Getter
@Setter
@ToString
public class TagDTO extends BaseDTO {

    /**
     * 标签序号
     */
    private Integer tagNo;
    /**
     * 标签类型
     */
    private String tagType;
    /**
     * 标签代码
     */
    private String tagCode;
    /**
     * 标签名称
     */
    private String tagName;
    /**
     * 标签值选项，多个值使用英文逗号分隔
     */
    private String tagValueOptions;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
