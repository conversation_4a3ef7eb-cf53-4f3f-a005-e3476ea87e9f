package com.xtc.marketing.xtcmarketing.dto.command;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 门店标签保存参数
 */
@Getter
@Setter
@ToString
public class ShopTagSaveCmd {

    /**
     * 门店id
     */
    @NotBlank
    @Length(max = 50)
    private String shopId;
    /**
     * 标签代码
     */
    @NotBlank
    @Length(max = 50)
    private String tagCode;
    /**
     * 标签值
     */
    @Length(max = 50)
    private String tagValue;

}
