package com.xtc.marketing.xtcmarketing.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 门店分页查询参数
 */
@Getter
@Setter
@ToString
public class ShopPageQry extends BasePageQuery {

    /**
     * 门店id
     */
    @Length(max = 50)
    private String shopId;
    /**
     * 门店名称
     */
    @Length(max = 20)
    private String shopName;

}
