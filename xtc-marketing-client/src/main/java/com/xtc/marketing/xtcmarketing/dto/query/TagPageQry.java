package com.xtc.marketing.xtcmarketing.dto.query;

import com.xtc.marketing.dto.BasePageQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 标签分页查询参数
 */
@Getter
@Setter
@ToString
public class TagPageQry extends BasePageQuery {

    /**
     * 标签序号
     */
    private Integer tagNo;
    /**
     * 标签类型
     */
    @Length(max = 20)
    private String tagType;
    /**
     * 标签代码
     */
    @Length(max = 50)
    private String tagCode;
    /**
     * 标签名称
     */
    @Length(max = 50)
    private String tagName;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
